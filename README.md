# SOL Trading Bot

A fully functional Python 3.11 Telegram bot that trades SOL/USDC via Jupiter v6 Aggregator with 5x leverage, operating 24/7 from Monday to Friday.

## 🚀 Features

### Trading Strategy
- **VWAP 60-minute + RSI 14-period** technical analysis
- **5x leverage** on all trades (90% of balance × 5x)
- **Fixed targets**: +0.20% profit, -0.15% stop loss
- **Daily target**: +0.20% on capital (auto-pause when reached)
- **Maximum daily risk**: 1% of capital
- **24/5 operation**: Monday to Friday continuous trading

### Position Sizing (Critical)
Each trade uses **90% of available balance multiplied by 5x leverage**:
- Position size calculation: `notional = balance × 0.90 × 5`
- Maximum loss per trade ≈ 0.9 × 5 × 0.15% = **0.675%** of capital
- Stays below 1% daily risk limit

### Security & Wallet Management
- **Self-custody wallet** with encrypted seed phrase storage
- **libsodium SecretBox** encryption with user password
- Secure key management with password protection
- Real-time deposit monitoring with notifications

### Real-time Configuration
- `/cfg` command displays current settings as JSON
- `/cfg set <KEY> <VALUE>` persists changes to SQLite database
- All trading parameters configurable on-the-fly

### Comprehensive Notifications
- Trade execution with 5x leverage confirmation
- Daily target achievement alerts
- Risk limit exceeded warnings
- Connection status and error notifications

## 📁 Project Structure

```
solbot/
├─ bot.py               # Telegram bot + command handlers
├─ scheduler.py         # 24/5 trading cycle management
├─ strategy.py          # VWAP + RSI signal generation
├─ trader.py            # Jupiter v6 execution, PnL tracking, 5x leverage
├─ wallet.py            # Encrypted keypair management
├─ risk.py              # Position sizing (90% × 5x), risk limits
├─ models.py            # SQLModel classes: Trade, Settings, Balance
├─ db.py                # SQLite session management
├─ utils.py             # VWAP, RSI calculations, deposit monitoring
├─ tests/
│   ├─ test_strategy.py # Strategy logic unit tests
│   └─ test_risk.py     # Risk management unit tests
├─ Dockerfile           # Production container setup
├─ docker-compose.yml   # Service orchestration
├─ requirements.txt     # Python dependencies
└─ .env.example         # Environment template
```

## 🛠️ Installation

### Prerequisites
- Python 3.11+
- Docker & Docker Compose (for production)
- Telegram Bot Token
- Solana RPC endpoint

### Local Development

1. **Clone and setup**:
```bash
git clone <repository>
cd solbot
cp .env.example .env
```

2. **Configure environment** (edit `.env`):
```bash
TELEGRAM_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_ID=your_telegram_user_id
SOLANA_RPC=https://api.mainnet-beta.solana.com
# ... other settings
```

3. **Install dependencies**:
```bash
pip install -r requirements.txt
```

4. **Run the bot**:
```bash
python -m bot
```

### Production Deployment

1. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with production values
```

2. **Deploy with Docker**:
```bash
docker-compose up -d
```

3. **Monitor logs**:
```bash
docker-compose logs -f solbot
```

## 📱 Telegram Commands

### Wallet & Balance
- `/wallet` - Show wallet address for deposits
- `/balance` - Show current SOL/USDC balances
- `/deposit` - Show deposit instructions
- `/withdraw <amount> <address>` - Withdraw funds (bot must be paused)

### Trading Control
- `/status` - Show bot status, balances, daily stats
- `/pause` - Pause trading (monitoring continues)
- `/resume` - Resume trading
- `/positions` - Show open positions with PnL
- `/trades` - Show recent trades (last 24h)

### Configuration
- `/cfg` - Show current configuration
- `/cfg set TARGET_PCT 0.003` - Set target to 0.30%
- `/cfg set LEVERAGE 3` - Change leverage to 3x
- `/cfg set MAX_TRADES 6` - Allow up to 6 trades per day

### Example Configuration Keys
```
TARGET_PCT=0.002      # 0.20% target per trade
STOP_PCT=0.0015       # 0.15% stop loss
TRADE_PCT=0.90        # 90% of balance per trade
LEVERAGE=5            # 5x leverage
MAX_TRADES=4          # Max 4 trades per day
MAX_DD_PCT=0.01       # 1% max daily drawdown
SLIPPAGE_BPS=15       # 0.15% slippage tolerance
```

## 🔧 Technical Implementation

### Trading Strategy Logic
```python
# LONG signal conditions
price < VWAP × 0.9975 AND RSI < 30

# SHORT signal conditions  
price > VWAP × 1.0025 AND RSI > 70
```

### Position Sizing Formula
```python
notional_usdc = balance_usdc × TRADE_PCT × LEVERAGE
sol_amount = notional_usdc / sol_price
max_loss = notional_usdc × STOP_PCT
risk_percentage = max_loss / balance_usdc
```

### 5x Leverage Implementation
The bot simulates 5x leverage by:
1. Using 90% of balance for actual SOL trades
2. Tracking notional position as 5x the trade amount
3. Calculating PnL based on 5x exposure
4. Applying leverage multiplier to profit/loss calculations

## 🧪 Testing

Run unit tests:
```bash
# All tests
pytest

# Strategy tests only
pytest tests/test_strategy.py

# Risk management tests only
pytest tests/test_risk.py

# With coverage
pytest --cov=. tests/
```

## 🔒 Security Features

### Wallet Security
- Encrypted seed phrase storage using libsodium
- Password-protected keystore
- No private keys in memory longer than necessary
- Secure key derivation with PBKDF2

### Operational Security
- Input validation and sanitization
- Rate limiting for API calls
- Graceful error handling
- Comprehensive logging for audit trails

### Risk Management
- Position size limits (max 90% of balance)
- Daily risk limits (max 1% drawdown)
- Stop-loss enforcement
- Daily target auto-pause

## 📊 Monitoring & Logging

### Database Schema
- **Trades**: Complete trade history with PnL tracking
- **Settings**: Dynamic configuration storage
- **Balances**: Regular balance snapshots
- **PriceData**: Historical price and indicator data

### Logging
- Structured JSON logging with timestamps
- Error tracking and alerting
- Performance metrics
- Trade execution audit trail

## 🚨 Risk Warnings

⚠️ **Important Disclaimers**:
- This bot trades with **5x leverage** - losses can be significant
- **Maximum loss per trade**: ~0.675% of capital
- **Daily risk limit**: 1% of total capital
- Always test with small amounts first
- Monitor the bot regularly
- Cryptocurrency trading involves substantial risk

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the logs: `docker-compose logs solbot`
2. Review configuration: `/cfg` command
3. Test with `/status` command
4. Check wallet balance: `/balance`

## 🔄 Updates

The bot supports automatic updates through configuration changes:
- Use `/cfg set` commands to modify parameters
- Changes persist across restarts
- No code changes required for most adjustments

---

**Built with**: Python 3.11, SQLModel, APScheduler, python-telegram-bot, Jupiter v6, Solana-py
