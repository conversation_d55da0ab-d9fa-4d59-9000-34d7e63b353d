"""
Database session management and initialization
"""
import os
import asyncio
from typing import As<PERSON><PERSON><PERSON>ator
from sqlmodel import SQLModel, create_engine, select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
import structlog

from models import Settings, DEFAULT_SETTINGS

logger = structlog.get_logger(__name__)


class DatabaseManager:
    """Manages database connections and initialization"""
    
    def __init__(self, database_path: str):
        self.database_path = database_path
        self.database_url = f"sqlite+aiosqlite:///{database_path}"
        
        # Create async engine with proper SQLite configuration
        self.engine = create_async_engine(
            self.database_url,
            echo=False,
            poolclass=StaticPool,
            connect_args={
                "check_same_thread": False,
                "timeout": 30,
            },
        )
    
    async def create_tables(self):
        """Create all database tables"""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(SQLModel.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise
    
    async def initialize_settings(self):
        """Initialize default settings if they don't exist"""
        try:
            async with AsyncSession(self.engine) as session:
                # Check if settings exist
                result = await session.execute(select(Settings))
                existing_settings = result.scalars().all()
                
                if not existing_settings:
                    logger.info("Initializing default settings")
                    
                    for key, value in DEFAULT_SETTINGS.items():
                        setting = Settings(
                            key=key,
                            value=value,
                            description=f"Default {key} setting"
                        )
                        session.add(setting)
                    
                    await session.commit()
                    logger.info("Default settings initialized")
                else:
                    logger.info(f"Found {len(existing_settings)} existing settings")
                    
        except Exception as e:
            logger.error("Failed to initialize settings", error=str(e))
            raise
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session"""
        async with AsyncSession(self.engine) as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error("Database session error", error=str(e))
                raise
            finally:
                await session.close()
    
    async def close(self):
        """Close database connections"""
        await self.engine.dispose()
        logger.info("Database connections closed")


# Global database manager instance
db_manager: DatabaseManager = None


async def init_database(database_path: str):
    """Initialize database manager"""
    global db_manager
    
    # Ensure data directory exists
    os.makedirs(os.path.dirname(database_path), exist_ok=True)
    
    db_manager = DatabaseManager(database_path)
    await db_manager.create_tables()
    await db_manager.initialize_settings()
    
    logger.info("Database initialized", path=database_path)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session - convenience function"""
    if db_manager is None:
        raise RuntimeError("Database not initialized. Call init_database() first.")
    
    async for session in db_manager.get_session():
        yield session


async def close_database():
    """Close database connections"""
    global db_manager
    if db_manager:
        await db_manager.close()
        db_manager = None


# Settings helper functions
async def get_setting(key: str, default: str = None) -> str:
    """Get a setting value by key"""
    async for session in get_db_session():
        result = await session.execute(select(Settings).where(Settings.key == key))
        setting = result.scalars().first()

        if setting:
            return setting.value
        elif default is not None:
            return default
        else:
            raise ValueError(f"Setting '{key}' not found and no default provided")


async def set_setting(key: str, value: str, description: str = None):
    """Set a setting value"""
    async for session in get_db_session():
        result = await session.execute(select(Settings).where(Settings.key == key))
        setting = result.scalars().first()

        if setting:
            setting.value = value
            if description:
                setting.description = description
        else:
            setting = Settings(
                key=key,
                value=value,
                description=description or f"Setting for {key}"
            )
            session.add(setting)

        await session.commit()
        logger.info("Setting updated", key=key, value=value)


async def get_all_settings() -> dict:
    """Get all settings as a dictionary"""
    async for session in get_db_session():
        result = await session.execute(select(Settings))
        settings = result.scalars().all()

        return {setting.key: setting.value for setting in settings}
