"""
Trading execution via Jupiter v6 Aggregator with 5x leverage
"""
import asyncio
from decimal import Decimal
from typing import Dict, Optional, Tuple
from datetime import datetime
import json

from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed
from solana.rpc.types import TxOpts
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solders.transaction import VersionedTransaction
import structlog

from models import Trade, Balance
from db import get_db_session
from wallet import get_wallet_manager
from risk import risk_manager, position_monitor
from utils import format_currency, calculate_pnl_percentage
from sqlmodel import select

logger = structlog.get_logger(__name__)


class JupiterTrader:
    """Jupiter v6 integration for SOL/USDC trading with leverage"""
    
    def __init__(self, rpc_url: str, jupiter_api_url: str = "https://quote-api.jup.ag/v6"):
        self.rpc_url = rpc_url
        self.jupiter_api_url = jupiter_api_url
        self.sol_mint = "So11111111111111111111111111111111111111112"
        self.usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        
    async def get_quote(self, input_mint: str, output_mint: str, amount: int, 
                       slippage_bps: int = 15) -> Optional[Dict]:
        """Get Jupiter swap quote"""
        import aiohttp
        
        params = {
            "inputMint": input_mint,
            "outputMint": output_mint,
            "amount": str(amount),
            "slippageBps": str(slippage_bps),
            "onlyDirectRoutes": "false",
            "asLegacyTransaction": "false"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.jupiter_api_url}/quote", params=params) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error("Jupiter quote failed", status=response.status)
                        return None
        except Exception as e:
            logger.error("Failed to get Jupiter quote", error=str(e))
            return None
    
    async def get_swap_transaction(self, quote: Dict, user_pubkey: str) -> Optional[str]:
        """Get swap transaction from Jupiter"""
        import aiohttp
        
        payload = {
            "quoteResponse": quote,
            "userPublicKey": user_pubkey,
            "wrapAndUnwrapSol": True,
            "dynamicComputeUnitLimit": True,
            "prioritizationFeeLamports": "auto"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.jupiter_api_url}/swap",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("swapTransaction")
                    else:
                        logger.error("Jupiter swap transaction failed", status=response.status)
                        return None
        except Exception as e:
            logger.error("Failed to get swap transaction", error=str(e))
            return None
    
    async def execute_swap(self, swap_transaction: str, keypair: Keypair) -> Optional[str]:
        """Execute swap transaction"""
        try:
            # Decode transaction
            transaction_bytes = bytes.fromhex(swap_transaction)
            transaction = VersionedTransaction.from_bytes(transaction_bytes)
            
            # Sign transaction
            transaction.sign([keypair])
            
            # Send transaction
            async with AsyncClient(self.rpc_url) as client:
                response = await client.send_transaction(
                    transaction,
                    opts=TxOpts(
                        skip_confirmation=False,
                        preflight_commitment=Confirmed,
                        max_retries=3
                    )
                )
                
                if response.value:
                    signature = str(response.value)
                    logger.info("Swap transaction sent", signature=signature)
                    
                    # Wait for confirmation
                    await self._wait_for_confirmation(client, signature)
                    return signature
                else:
                    logger.error("Failed to send swap transaction")
                    return None
                    
        except Exception as e:
            logger.error("Failed to execute swap", error=str(e))
            return None
    
    async def _wait_for_confirmation(self, client: AsyncClient, signature: str, 
                                   max_retries: int = 30):
        """Wait for transaction confirmation"""
        for i in range(max_retries):
            try:
                response = await client.get_signature_statuses([signature])
                if response.value and response.value[0]:
                    status = response.value[0]
                    if status.confirmation_status == "confirmed":
                        logger.info("Transaction confirmed", signature=signature)
                        return True
                    elif status.err:
                        logger.error("Transaction failed", signature=signature, error=status.err)
                        return False
                
                await asyncio.sleep(2)  # Wait 2 seconds between checks
                
            except Exception as e:
                logger.warning("Error checking transaction status", error=str(e))
                await asyncio.sleep(2)
        
        logger.warning("Transaction confirmation timeout", signature=signature)
        return False


class LeverageTrader:
    """Handles leveraged trading logic"""
    
    def __init__(self, jupiter_trader: JupiterTrader):
        self.jupiter_trader = jupiter_trader
        self.active_positions: Dict[int, Dict] = {}
    
    async def open_position(self, side: str, sol_price: Decimal, 
                          balance_usdc: Decimal) -> Optional[Dict]:
        """
        Open leveraged position (90% of balance × 5x leverage)
        
        For 5x leverage simulation:
        1. Use 90% of balance to buy/sell SOL
        2. Track notional position as 5x the actual trade
        3. Calculate PnL based on 5x notional exposure
        """
        try:
            # Update risk parameters
            await risk_manager.update_from_settings()
            
            # Validate trade parameters
            validation = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
            if not validation["valid"]:
                logger.warning("Trade validation failed", reason=validation["reason"])
                return None
            
            position_info = validation["position_info"]
            
            # Get wallet manager
            wallet_mgr = get_wallet_manager()
            
            # Calculate actual SOL amount to trade (90% of balance)
            actual_usdc_amount = balance_usdc * risk_manager.trade_pct
            actual_sol_amount = actual_usdc_amount / sol_price
            
            # Execute the base trade
            if side.upper() == "LONG":
                # Buy SOL with USDC
                signature = await self._buy_sol(actual_usdc_amount, wallet_mgr.keypair)
            else:
                # Sell SOL for USDC  
                signature = await self._sell_sol(actual_sol_amount, wallet_mgr.keypair)
            
            if not signature:
                logger.error("Failed to execute base trade")
                return None
            
            # Calculate target and stop prices
            target_price, stop_price = risk_manager.calculate_target_stop_prices(
                sol_price, side
            )
            
            # Create trade record with 5x leverage tracking
            trade_data = {
                "signature": signature,
                "side": side.upper(),
                "entry_price": sol_price,
                "size_sol": position_info["sol_amount"],  # 5x notional amount
                "notional_usdc": position_info["notional_usdc"],  # 5x notional
                "leverage": position_info["leverage_used"],
                "target_price": target_price,
                "stop_price": stop_price,
                "status": "OPEN",
                "strategy_signal": "VWAP_RSI",
                "fees_paid": Decimal("0")  # Will be updated after fee calculation
            }
            
            # Save to database
            trade_id = await self._save_trade(trade_data)
            
            if trade_id:
                # Track position for monitoring
                self.active_positions[trade_id] = {
                    "trade_id": trade_id,
                    "side": side.upper(),
                    "entry_price": float(sol_price),
                    "notional_usdc": float(position_info["notional_usdc"]),
                    "leverage": float(position_info["leverage_used"]),
                    "target_price": float(target_price),
                    "stop_price": float(stop_price)
                }
                
                logger.info("Position opened successfully",
                           trade_id=trade_id,
                           side=side,
                           entry_price=float(sol_price),
                           notional_usdc=float(position_info["notional_usdc"]),
                           leverage=float(position_info["leverage_used"]),
                           signature=signature)
                
                return {
                    "trade_id": trade_id,
                    "signature": signature,
                    "side": side.upper(),
                    "entry_price": float(sol_price),
                    "notional_usdc": float(position_info["notional_usdc"]),
                    "leverage": float(position_info["leverage_used"]),
                    "target_price": float(target_price),
                    "stop_price": float(stop_price),
                    "max_loss_usdc": float(position_info["max_loss_usdc"])
                }
            
            return None

        except Exception as e:
            logger.error("Failed to open position", error=str(e))
            return None

    async def close_position(self, trade_id: int, current_price: Decimal,
                           reason: str = "Manual") -> Optional[Dict]:
        """Close leveraged position and calculate PnL"""
        try:
            # Get trade from database
            async for session in get_db_session():
                trade = await session.get(Trade, trade_id)
                if not trade or trade.status != "OPEN":
                    logger.warning("Trade not found or already closed", trade_id=trade_id)
                    return None

                # Calculate PnL based on 5x leverage
                pnl_data = self._calculate_leveraged_pnl(trade, current_price)

                # Execute closing trade
                wallet_mgr = get_wallet_manager()

                if trade.side == "LONG":
                    # Sell SOL back to USDC
                    actual_sol_amount = (trade.notional_usdc / Decimal("5")) / trade.entry_price
                    signature = await self._sell_sol(actual_sol_amount, wallet_mgr.keypair)
                else:
                    # Buy SOL back with USDC
                    actual_usdc_amount = (trade.notional_usdc / Decimal("5"))
                    signature = await self._buy_sol(actual_usdc_amount, wallet_mgr.keypair)

                if not signature:
                    logger.error("Failed to execute closing trade")
                    return None

                # Update trade record
                trade.exit_price = current_price
                trade.exit_time = datetime.now(datetime.UTC)
                trade.realized_pnl = pnl_data["realized_pnl"]
                trade.status = "CLOSED"
                trade.notes = f"Closed: {reason}"

                await session.commit()

                # Remove from active positions
                if trade_id in self.active_positions:
                    del self.active_positions[trade_id]

                logger.info("Position closed successfully",
                           trade_id=trade_id,
                           exit_price=float(current_price),
                           realized_pnl=float(pnl_data["realized_pnl"]),
                           reason=reason)

                return {
                    "trade_id": trade_id,
                    "signature": signature,
                    "exit_price": float(current_price),
                    "realized_pnl": float(pnl_data["realized_pnl"]),
                    "pnl_percentage": pnl_data["pnl_percentage"],
                    "reason": reason
                }

        except Exception as e:
            logger.error("Failed to close position", error=str(e))
            return None

    def _calculate_leveraged_pnl(self, trade: Trade, current_price: Decimal) -> Dict:
        """Calculate PnL with 5x leverage"""
        entry_price = trade.entry_price
        notional_usdc = trade.notional_usdc
        side = trade.side

        # Calculate price change percentage
        if side == "LONG":
            price_change_pct = (current_price - entry_price) / entry_price
        else:  # SHORT
            price_change_pct = (entry_price - current_price) / entry_price

        # Apply 5x leverage to PnL
        leveraged_pnl_pct = price_change_pct * trade.leverage
        realized_pnl = notional_usdc * leveraged_pnl_pct

        return {
            "realized_pnl": realized_pnl,
            "pnl_percentage": float(leveraged_pnl_pct * 100),
            "price_change_pct": float(price_change_pct * 100)
        }

    async def _buy_sol(self, usdc_amount: Decimal, keypair: Keypair) -> Optional[str]:
        """Buy SOL with USDC via Jupiter"""
        try:
            # Convert USDC amount to smallest unit (6 decimals)
            usdc_amount_raw = int(usdc_amount * 1_000_000)

            # Get quote for USDC -> SOL
            quote = await self.jupiter_trader.get_quote(
                self.jupiter_trader.usdc_mint,
                self.jupiter_trader.sol_mint,
                usdc_amount_raw
            )

            if not quote:
                logger.error("Failed to get buy quote")
                return None

            # Get swap transaction
            swap_tx = await self.jupiter_trader.get_swap_transaction(
                quote, str(keypair.pubkey())
            )

            if not swap_tx:
                logger.error("Failed to get buy swap transaction")
                return None

            # Execute swap
            signature = await self.jupiter_trader.execute_swap(swap_tx, keypair)

            if signature:
                logger.info("SOL purchase executed",
                           usdc_amount=float(usdc_amount),
                           signature=signature)

            return signature

        except Exception as e:
            logger.error("Failed to buy SOL", error=str(e))
            return None

    async def _sell_sol(self, sol_amount: Decimal, keypair: Keypair) -> Optional[str]:
        """Sell SOL for USDC via Jupiter"""
        try:
            # Convert SOL amount to smallest unit (9 decimals)
            sol_amount_raw = int(sol_amount * 1_000_000_000)

            # Get quote for SOL -> USDC
            quote = await self.jupiter_trader.get_quote(
                self.jupiter_trader.sol_mint,
                self.jupiter_trader.usdc_mint,
                sol_amount_raw
            )

            if not quote:
                logger.error("Failed to get sell quote")
                return None

            # Get swap transaction
            swap_tx = await self.jupiter_trader.get_swap_transaction(
                quote, str(keypair.pubkey())
            )

            if not swap_tx:
                logger.error("Failed to get sell swap transaction")
                return None

            # Execute swap
            signature = await self.jupiter_trader.execute_swap(swap_tx, keypair)

            if signature:
                logger.info("SOL sale executed",
                           sol_amount=float(sol_amount),
                           signature=signature)

            return signature

        except Exception as e:
            logger.error("Failed to sell SOL", error=str(e))
            return None

    async def _save_trade(self, trade_data: Dict) -> Optional[int]:
        """Save trade to database"""
        try:
            async for session in get_db_session():
                trade = Trade(**trade_data)
                session.add(trade)
                await session.commit()
                await session.refresh(trade)
                return trade.id
        except Exception as e:
            logger.error("Failed to save trade", error=str(e))
            return None

    async def update_position_pnl(self, current_sol_price: Decimal):
        """Update unrealized PnL for all open positions"""
        try:
            async for session in get_db_session():

                result = await session.exec(select(Trade).where(Trade.status == "OPEN"))
                open_trades = result.all()

                for trade in open_trades:
                    pnl_data = self._calculate_leveraged_pnl(trade, current_sol_price)
                    trade.unrealized_pnl = pnl_data["realized_pnl"]

                await session.commit()

        except Exception as e:
            logger.error("Failed to update position PnL", error=str(e))


# Global trader instances
jupiter_trader: Optional[JupiterTrader] = None
leverage_trader: Optional[LeverageTrader] = None


def init_traders(rpc_url: str) -> Tuple[JupiterTrader, LeverageTrader]:
    """Initialize global trader instances"""
    global jupiter_trader, leverage_trader

    jupiter_trader = JupiterTrader(rpc_url)
    leverage_trader = LeverageTrader(jupiter_trader)

    return jupiter_trader, leverage_trader


def get_traders() -> Tuple[JupiterTrader, LeverageTrader]:
    """Get global trader instances"""
    if jupiter_trader is None or leverage_trader is None:
        raise RuntimeError("Traders not initialized")
    return jupiter_trader, leverage_trader
