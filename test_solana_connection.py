#!/usr/bin/env python3
"""
Teste de conexão com a rede Solana
"""
import asyncio
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

async def test_solana_connection():
    """Testar conexão com Solana RPC"""
    print("🔗 Testando conexão com Solana...")
    
    rpc_url = os.getenv("SOLANA_RPC", "https://api.mainnet-beta.solana.com")
    print(f"📡 RPC URL: {rpc_url}")
    
    try:
        # Teste básico com aiohttp
        import aiohttp
        import json
        
        # Teste de conexão RPC
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getHealth"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Solana RPC conectado: {result}")
                else:
                    print(f"❌ Erro RPC: Status {response.status}")
                    return False
        
        # Teste de slot atual
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getSlot"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    slot = result.get("result", 0)
                    print(f"✅ Slot atual: {slot}")
                else:
                    print(f"❌ Erro ao obter slot")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro de conexão Solana: {e}")
        return False

async def test_jupiter_api():
    """Testar conexão com Jupiter API"""
    print("\n🪐 Testando conexão com Jupiter...")
    
    try:
        import aiohttp
        
        jupiter_url = "https://quote-api.jup.ag/v6"
        
        # Teste básico de quote
        params = {
            "inputMint": "So11111111111111111111111111111111111111112",  # SOL
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "amount": "1000000000",  # 1 SOL
            "slippageBps": "50"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{jupiter_url}/quote", params=params) as response:
                if response.status == 200:
                    result = await response.json()
                    input_amount = int(result.get("inAmount", 0)) / 1_000_000_000
                    output_amount = int(result.get("outAmount", 0)) / 1_000_000
                    print(f"✅ Jupiter API conectado")
                    print(f"📊 Quote: {input_amount} SOL → {output_amount} USDC")
                    return True
                else:
                    print(f"❌ Erro Jupiter API: Status {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Erro Jupiter API: {e}")
        return False

async def test_wallet_connection():
    """Testar carteira Solana"""
    print("\n💰 Testando carteira...")
    
    try:
        from wallet import get_wallet_manager
        
        # Inicializar wallet manager
        wallet_mgr = get_wallet_manager()
        
        if wallet_mgr and wallet_mgr.keypair:
            address = str(wallet_mgr.keypair.pubkey())
            print(f"✅ Carteira carregada: {address}")
            
            # Testar conexão com a carteira na rede
            rpc_url = os.getenv("SOLANA_RPC")
            import aiohttp
            
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": [address, {"encoding": "base64"}]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(rpc_url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        account_info = result.get("result", {}).get("value")
                        if account_info:
                            lamports = account_info.get("lamports", 0)
                            sol_balance = lamports / 1_000_000_000
                            print(f"✅ Saldo SOL: {sol_balance:.6f}")
                        else:
                            print("✅ Conta existe na rede (saldo zero)")
                        return True
                    else:
                        print(f"❌ Erro ao verificar conta: Status {response.status}")
                        return False
        else:
            print("❌ Carteira não carregada")
            return False
            
    except Exception as e:
        print(f"❌ Erro na carteira: {e}")
        return False

async def test_pyth_price_feed():
    """Testar feed de preços Pyth"""
    print("\n📈 Testando feed de preços Pyth...")
    
    try:
        sol_price_feed = os.getenv("SOL_PRICE_FEED", "H6ARHf6YXhGYeQfUzQNGk6rDNnLBQKrenN712K4AQJEG")
        rpc_url = os.getenv("SOLANA_RPC")
        
        import aiohttp
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [sol_price_feed, {"encoding": "base64"}]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    account_info = result.get("result", {}).get("value")
                    if account_info:
                        print(f"✅ Feed de preços Pyth ativo")
                        print(f"📍 Address: {sol_price_feed}")
                        return True
                    else:
                        print(f"❌ Feed de preços não encontrado")
                        return False
                else:
                    print(f"❌ Erro ao verificar feed: Status {response.status}")
                    return False
                    
    except Exception as e:
        print(f"❌ Erro no feed de preços: {e}")
        return False

async def main():
    """Função principal de teste"""
    print("🔍 Verificação Completa da Operação Solana\n")
    
    # Executar todos os testes
    solana_ok = await test_solana_connection()
    jupiter_ok = await test_jupiter_api()
    wallet_ok = await test_wallet_connection()
    pyth_ok = await test_pyth_price_feed()
    
    print("\n📋 Resumo dos Testes:")
    print(f"✅ Solana RPC: {'OK' if solana_ok else 'ERRO'}")
    print(f"✅ Jupiter API: {'OK' if jupiter_ok else 'ERRO'}")
    print(f"✅ Carteira: {'OK' if wallet_ok else 'ERRO'}")
    print(f"✅ Pyth Feed: {'OK' if pyth_ok else 'ERRO'}")
    
    if all([solana_ok, jupiter_ok, wallet_ok, pyth_ok]):
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Bot está operando corretamente na rede Solana")
        print("✅ Pronto para trading automático")
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM")
        print("❌ Verifique as configurações antes de usar")
    
    print(f"\n📊 Configurações atuais:")
    print(f"   • Rede: Solana Mainnet")
    print(f"   • RPC: {os.getenv('SOLANA_RPC')}")
    print(f"   • Jupiter: v6 API")
    print(f"   • Pyth: Feed de preços SOL/USD")

if __name__ == "__main__":
    asyncio.run(main())
