"""
Encrypted wallet management with libsodium SecretBox
"""
import os
import json
import getpass
from datetime import datetime
from typing import Op<PERSON>, Tuple
from solders.keypair import Keypair
from solders.pubkey import Pubkey
from solana.rpc.async_api import AsyncClient
from solana.rpc.types import TokenAccountOpts
import nacl.secret
import nacl.utils
import nacl.exceptions
import structlog

logger = structlog.get_logger(__name__)


class WalletManager:
    """Manages encrypted wallet storage and operations"""
    
    def __init__(self, keystore_path: str, rpc_url: str):
        self.keystore_path = keystore_path
        self.rpc_url = rpc_url
        self.keypair: Optional[Keypair] = None
        self.password: Optional[str] = None
        
        # Ensure keystore directory exists
        os.makedirs(os.path.dirname(keystore_path), exist_ok=True)
    
    def _derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password using PBKDF2"""
        import hashlib
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000, 32)
    
    def _encrypt_seed(self, seed_phrase: str, password: str) -> bytes:
        """Encrypt seed phrase using libsodium SecretBox"""
        # Generate random salt
        salt = nacl.utils.random(16)
        
        # Derive key from password
        key = self._derive_key(password, salt)
        
        # Create SecretBox
        box = nacl.secret.SecretBox(key)
        
        # Encrypt seed phrase
        encrypted = box.encrypt(seed_phrase.encode())
        
        # Combine salt + encrypted data
        return salt + encrypted
    
    def _decrypt_seed(self, encrypted_data: bytes, password: str) -> str:
        """Decrypt seed phrase using libsodium SecretBox"""
        # Extract salt and encrypted data
        salt = encrypted_data[:16]
        encrypted = encrypted_data[16:]
        
        # Derive key from password
        key = self._derive_key(password, salt)
        
        # Create SecretBox
        box = nacl.secret.SecretBox(key)
        
        # Decrypt seed phrase
        decrypted = box.decrypt(encrypted)
        return decrypted.decode()

    def _save_seed_to_txt(self, seed_phrase: str, address: str, user_password: str):
        """Save seed phrase to TXT file with fixed password drfs2015"""
        # Create TXT file path
        txt_path = self.keystore_path.replace('.bin', '_backup.txt')

        # Fixed password as requested
        fixed_password = "drfs2015"

        # Create content for TXT file
        content = f"""SOL Trading Bot - Wallet Backup
=====================================

Endereço da Carteira: {address}
Palavra-passe do Utilizador: {user_password}
Palavra-passe de Backup: {fixed_password}

Seed Phrase (Private Key):
{seed_phrase}

=====================================
IMPORTANTE:
- Guarde este arquivo em local seguro
- Nunca partilhe a seed phrase
- Use a palavra-passe de backup: {fixed_password}
- Data de criação: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
=====================================
"""

        # Save to TXT file
        try:
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info("Seed phrase saved to TXT file",
                       txt_path=txt_path,
                       address=address)

            print(f"✅ Backup da carteira salvo em: {txt_path}")
            print(f"🔑 Palavra-passe de backup: {fixed_password}")

        except Exception as e:
            logger.error("Failed to save seed to TXT", error=str(e))
            print(f"❌ Erro ao salvar backup: {e}")

    def generate_new_wallet(self, password: str) -> Tuple[Keypair, str]:
        """Generate new wallet and save encrypted"""
        if len(password) < 12:
            raise ValueError("Password must be at least 12 characters long")

        # Generate new keypair
        keypair = Keypair()

        # Get seed phrase (private key as hex for simplicity)
        seed_phrase = str(keypair.secret())

        # Encrypt and save
        encrypted_data = self._encrypt_seed(seed_phrase, password)

        with open(self.keystore_path, 'wb') as f:
            f.write(encrypted_data)

        # Save seed phrase in TXT file with fixed password "drfs2015"
        self._save_seed_to_txt(seed_phrase, str(keypair.pubkey()), password)

        self.keypair = keypair
        self.password = password

        logger.info("New wallet generated",
                   address=str(keypair.pubkey()),
                   keystore_path=self.keystore_path)

        return keypair, str(keypair.pubkey())
    
    def load_wallet(self, password: str) -> Keypair:
        """Load wallet from encrypted keystore"""
        if not os.path.exists(self.keystore_path):
            raise FileNotFoundError(f"Keystore not found: {self.keystore_path}")
        
        try:
            with open(self.keystore_path, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt seed phrase
            seed_phrase = self._decrypt_seed(encrypted_data, password)
            
            # Recreate keypair from seed
            secret_key = int(seed_phrase)
            keypair = Keypair.from_bytes(secret_key.to_bytes(32, 'big'))

            # Save backup to TXT file when loading existing wallet
            self._save_seed_to_txt(seed_phrase, str(keypair.pubkey()), password)

            self.keypair = keypair
            self.password = password

            logger.info("Wallet loaded successfully",
                       address=str(keypair.pubkey()))

            return keypair
            
        except nacl.exceptions.InvalidMessage:
            raise ValueError("Invalid password or corrupted keystore")
        except Exception as e:
            logger.error("Failed to load wallet", error=str(e))
            raise
    
    def get_wallet_address(self) -> str:
        """Get wallet public address"""
        if not self.keypair:
            raise RuntimeError("Wallet not loaded")
        return str(self.keypair.pubkey())
    
    async def get_sol_balance(self) -> float:
        """Get SOL balance"""
        if not self.keypair:
            raise RuntimeError("Wallet not loaded")
        
        async with AsyncClient(self.rpc_url) as client:
            response = await client.get_balance(self.keypair.pubkey())
            if response.value is not None:
                return response.value / 1e9  # Convert lamports to SOL
            return 0.0
    
    async def get_usdc_balance(self) -> float:
        """Get USDC balance"""
        if not self.keypair:
            raise RuntimeError("Wallet not loaded")
        
        # USDC mint address on mainnet
        usdc_mint = Pubkey.from_string("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")
        
        async with AsyncClient(self.rpc_url) as client:
            try:
                response = await client.get_token_accounts_by_owner(
                    self.keypair.pubkey(),
                    TokenAccountOpts(mint=usdc_mint)
                )
                
                if response.value:
                    # Get balance from first USDC account
                    account = response.value[0]
                    balance_response = await client.get_token_account_balance(
                        account.pubkey
                    )
                    if balance_response.value:
                        return float(balance_response.value.ui_amount or 0)
                
                return 0.0
                
            except Exception as e:
                logger.warning("Failed to get USDC balance", error=str(e))
                return 0.0
    
    async def get_balances(self) -> dict:
        """Get both SOL and USDC balances"""
        sol_balance = await self.get_sol_balance()
        usdc_balance = await self.get_usdc_balance()
        
        return {
            "SOL": sol_balance,
            "USDC": usdc_balance,
            "address": self.get_wallet_address()
        }
    
    def wallet_exists(self) -> bool:
        """Check if wallet keystore exists"""
        return os.path.exists(self.keystore_path)
    
    def setup_wallet_interactive(self) -> Tuple[Keypair, str]:
        """Interactive wallet setup"""
        if self.wallet_exists():
            print("Existing wallet found.")
            password = getpass.getpass("Enter wallet password: ")
            try:
                keypair = self.load_wallet(password)
                return keypair, str(keypair.pubkey())
            except ValueError:
                print("Invalid password!")
                raise
        else:
            print("No wallet found. Creating new wallet...")
            password = getpass.getpass("Enter new wallet password (min 12 chars): ")
            confirm_password = getpass.getpass("Confirm password: ")
            
            if password != confirm_password:
                raise ValueError("Passwords do not match!")
            
            if len(password) < 12:
                raise ValueError("Password must be at least 12 characters long!")
            
            return self.generate_new_wallet(password)


# Global wallet manager instance
wallet_manager: Optional[WalletManager] = None


def init_wallet_manager(keystore_path: str, rpc_url: str):
    """Initialize global wallet manager"""
    global wallet_manager
    wallet_manager = WalletManager(keystore_path, rpc_url)
    return wallet_manager


def get_wallet_manager() -> WalletManager:
    """Get global wallet manager instance"""
    if wallet_manager is None:
        raise RuntimeError("Wallet manager not initialized")
    return wallet_manager
