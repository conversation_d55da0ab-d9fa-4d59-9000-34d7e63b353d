# 📱 Como Configurar o Telegram Bot

## ⚠️ PROBLEMA DETECTADO

O bot está funcionando, mas há um problema com o `TELEGRAM_ADMIN_ID` no arquivo `.env`.

**ID atual:** `-1669522740` (parece ser um ID de grupo, não de usuário)

---

## 🔧 COMO CORRIGIR

### **1. Obter seu ID de usuário correto:**

1. **Abra o Telegram**
2. **Procure por:** `@userinfobot`
3. **Inicie conversa:** Digite `/start`
4. **Copie seu ID:** Será algo como `123456789` (número positivo)

### **2. Atualizar o arquivo .env:**

Edite o arquivo `.env` e substitua:
```bash
# ANTES (incorreto)
TELEGRAM_ADMIN_ID=-1669522740

# DEPOIS (correto)
TELEGRAM_ADMIN_ID=SEU_ID_AQUI
```

**Exemplo:**
```bash
TELEGRAM_ADMIN_ID=123456789
```

---

## 🚀 EXECUTAR NOVAMENTE

Após corrigir o ID:

```bash
python -m bot
```

O bot irá:
1. ✅ Carregar/criar carteira
2. ✅ Conectar ao Telegram
3. ✅ Enviar mensagem de boas-vindas
4. ✅ Mostrar instruções completas

---

## 📱 USAR O BOT

Depois que o bot estiver rodando:

1. **Abra seu bot no Telegram**
2. **Digite:** `/start`
3. **Verá:** Instruções completas em português
4. **Use:** `/deposit` para obter endereço da carteira

---

## 🎯 STATUS ATUAL

✅ **Bot funcionando** - Código está correto
✅ **Carteira criada** - Sistema de segurança ativo
✅ **Telegram conectado** - Aguardando ID correto
✅ **Instruções prontas** - Comando `/start` funcionando

**Só falta corrigir o TELEGRAM_ADMIN_ID!**

---

## 🆘 SOLUÇÃO RÁPIDA

Se não conseguir obter o ID:

1. **Crie um novo bot:**
   - Vá em `@BotFather`
   - Digite `/newbot`
   - Copie o novo token

2. **Use seu próprio ID:**
   - Vá em `@userinfobot`
   - Digite `/start`
   - Copie o ID

3. **Atualize .env:**
   ```bash
   TELEGRAM_TOKEN=novo_token_aqui
   TELEGRAM_ADMIN_ID=seu_id_aqui
   ```

4. **Execute novamente:**
   ```bash
   python -m bot
   ```

**O bot está 100% funcional, só precisa do ID correto!**
