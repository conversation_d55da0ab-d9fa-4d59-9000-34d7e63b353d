"""
Risk management and position sizing (90% × 5x leverage)
"""
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Tu<PERSON>, Optional, List
from datetime import datetime, timedelta
import structlog

from models import Trade, Balance
from db import get_db_session, get_setting
from sqlmodel import select, func

logger = structlog.get_logger(__name__)


class RiskManager:
    """Manages position sizing and risk limits"""
    
    def __init__(self):
        self.max_daily_risk = Decimal("0.01")  # 1% max daily risk
        self.trade_pct = Decimal("0.90")       # 90% of balance per trade
        self.leverage = Decimal("5.0")         # 5x leverage
        self.target_pct = Decimal("0.002")     # 0.20% target
        self.stop_pct = Decimal("0.0015")      # 0.15% stop loss
    
    async def update_from_settings(self):
        """Update risk parameters from database settings"""
        try:
            self.trade_pct = Decimal(await get_setting("TRADE_PCT", "0.90"))
            self.leverage = Decimal(await get_setting("LEVERAGE", "5"))
            self.target_pct = Decimal(await get_setting("TARGET_PCT", "0.002"))
            self.stop_pct = Decimal(await get_setting("STOP_PCT", "0.0015"))
            self.max_daily_risk = Decimal(await get_setting("MAX_DD_PCT", "0.01"))
            
            logger.info("Risk parameters updated from settings",
                       trade_pct=float(self.trade_pct),
                       leverage=float(self.leverage),
                       target_pct=float(self.target_pct),
                       stop_pct=float(self.stop_pct))
        except Exception as e:
            logger.warning("Failed to update risk parameters", error=str(e))
    
    def calculate_position_size(self, balance_usdc: Decimal, sol_price: Decimal) -> Dict[str, Decimal]:
        """
        Calculate position size with 90% of balance × 5x leverage
        
        Returns:
            - notional_usdc: Total notional value in USDC
            - sol_amount: SOL amount to trade
            - max_loss_usdc: Maximum potential loss
            - risk_percentage: Risk as percentage of total balance
        """
        # Calculate notional position size
        notional_usdc = balance_usdc * self.trade_pct * self.leverage
        
        # Calculate SOL amount
        sol_amount = notional_usdc / sol_price
        
        # Calculate maximum potential loss
        # Max loss = notional × stop_pct = balance × trade_pct × leverage × stop_pct
        max_loss_usdc = notional_usdc * self.stop_pct
        
        # Risk as percentage of total balance
        risk_percentage = max_loss_usdc / balance_usdc
        
        return {
            "notional_usdc": notional_usdc.quantize(Decimal("0.01"), rounding=ROUND_DOWN),
            "sol_amount": sol_amount.quantize(Decimal("0.0001"), rounding=ROUND_DOWN),
            "max_loss_usdc": max_loss_usdc.quantize(Decimal("0.01"), rounding=ROUND_DOWN),
            "risk_percentage": risk_percentage.quantize(Decimal("0.0001"), rounding=ROUND_DOWN),
            "leverage_used": self.leverage
        }
    
    def calculate_target_stop_prices(self, entry_price: Decimal, side: str) -> Tuple[Decimal, Decimal]:
        """Calculate target and stop loss prices"""
        if side.upper() == "LONG":
            target_price = entry_price * (Decimal("1") + self.target_pct)
            stop_price = entry_price * (Decimal("1") - self.stop_pct)
        else:  # SHORT
            target_price = entry_price * (Decimal("1") - self.target_pct)
            stop_price = entry_price * (Decimal("1") + self.stop_pct)
        
        return (
            target_price.quantize(Decimal("0.0001"), rounding=ROUND_DOWN),
            stop_price.quantize(Decimal("0.0001"), rounding=ROUND_DOWN)
        )
    
    async def check_daily_risk_limits(self) -> Dict[str, any]:
        """Check if daily risk limits are exceeded"""
        today = datetime.utcnow().date()
        
        async for session in get_db_session():
            # Get today's trades
            result = await session.exec(
                select(Trade).where(
                    func.date(Trade.entry_time) == today
                )
            )
            todays_trades = result.all()
            
            # Calculate daily PnL
            daily_pnl = Decimal("0")
            for trade in todays_trades:
                if trade.realized_pnl:
                    daily_pnl += trade.realized_pnl
            
            # Get current balance
            balance_result = await session.exec(
                select(Balance).order_by(Balance.timestamp.desc()).limit(1)
            )
            current_balance = balance_result.first()
            
            if not current_balance:
                return {
                    "within_limits": True,
                    "daily_pnl": 0.0,
                    "daily_pnl_pct": 0.0,
                    "trades_today": 0,
                    "max_trades": int(await get_setting("MAX_TRADES", "4"))
                }
            
            # Calculate daily PnL percentage
            daily_pnl_pct = daily_pnl / current_balance.total_value_usdc
            
            # Check limits
            max_trades = int(await get_setting("MAX_TRADES", "4"))
            within_limits = (
                len(todays_trades) < max_trades and
                abs(daily_pnl_pct) < self.max_daily_risk
            )
            
            return {
                "within_limits": within_limits,
                "daily_pnl": float(daily_pnl),
                "daily_pnl_pct": float(daily_pnl_pct),
                "trades_today": len(todays_trades),
                "max_trades": max_trades,
                "risk_limit": float(self.max_daily_risk)
            }
    
    async def check_daily_target_reached(self) -> bool:
        """Check if daily target of +0.20% has been reached"""
        risk_status = await self.check_daily_risk_limits()
        
        # Daily target is +0.20% (same as individual trade target)
        daily_target = float(self.target_pct)
        
        return risk_status["daily_pnl_pct"] >= daily_target
    
    def validate_trade_parameters(self, balance_usdc: Decimal, sol_price: Decimal) -> Dict[str, any]:
        """Validate if trade can be executed within risk parameters"""
        if balance_usdc <= 0:
            return {
                "valid": False,
                "reason": "Insufficient balance"
            }
        
        if sol_price <= 0:
            return {
                "valid": False,
                "reason": "Invalid SOL price"
            }
        
        position_info = self.calculate_position_size(balance_usdc, sol_price)
        
        # Check if risk is within daily limit
        if position_info["risk_percentage"] > self.max_daily_risk:
            return {
                "valid": False,
                "reason": f"Trade risk {position_info['risk_percentage']:.2%} exceeds daily limit {self.max_daily_risk:.2%}"
            }
        
        # Check minimum position size
        min_notional = Decimal("10.0")  # $10 minimum
        if position_info["notional_usdc"] < min_notional:
            return {
                "valid": False,
                "reason": f"Position size ${position_info['notional_usdc']} below minimum ${min_notional}"
            }
        
        return {
            "valid": True,
            "position_info": position_info
        }
    
    async def log_risk_metrics(self, trade_data: Dict):
        """Log risk metrics for monitoring"""
        logger.info("Trade risk metrics",
                   notional_usdc=trade_data.get("notional_usdc"),
                   leverage=trade_data.get("leverage_used"),
                   max_loss_usdc=trade_data.get("max_loss_usdc"),
                   risk_percentage=trade_data.get("risk_percentage"),
                   target_pct=float(self.target_pct),
                   stop_pct=float(self.stop_pct))


class PositionMonitor:
    """Monitors open positions for target/stop conditions"""
    
    def __init__(self, risk_manager: RiskManager):
        self.risk_manager = risk_manager
    
    async def check_open_positions(self, current_sol_price: Decimal) -> List[Dict]:
        """Check all open positions against target/stop prices"""
        async for session in get_db_session():
            result = await session.exec(
                select(Trade).where(Trade.status == "OPEN")
            )
            open_trades = result.all()
            
            actions_needed = []
            
            for trade in open_trades:
                action = self._check_position_exit(trade, current_sol_price)
                if action:
                    actions_needed.append(action)
            
            return actions_needed
    
    def _check_position_exit(self, trade: Trade, current_price: Decimal) -> Optional[Dict]:
        """Check if position should be closed"""
        if not trade.target_price or not trade.stop_price:
            return None
        
        side = trade.side.upper()
        
        # Check target hit
        if side == "LONG" and current_price >= trade.target_price:
            return {
                "action": "CLOSE_TARGET",
                "trade_id": trade.id,
                "side": side,
                "current_price": current_price,
                "target_price": trade.target_price,
                "reason": "Target reached"
            }
        elif side == "SHORT" and current_price <= trade.target_price:
            return {
                "action": "CLOSE_TARGET",
                "trade_id": trade.id,
                "side": side,
                "current_price": current_price,
                "target_price": trade.target_price,
                "reason": "Target reached"
            }
        
        # Check stop loss hit
        if side == "LONG" and current_price <= trade.stop_price:
            return {
                "action": "CLOSE_STOP",
                "trade_id": trade.id,
                "side": side,
                "current_price": current_price,
                "stop_price": trade.stop_price,
                "reason": "Stop loss triggered"
            }
        elif side == "SHORT" and current_price >= trade.stop_price:
            return {
                "action": "CLOSE_STOP",
                "trade_id": trade.id,
                "side": side,
                "current_price": current_price,
                "stop_price": trade.stop_price,
                "reason": "Stop loss triggered"
            }
        
        return None


# Global risk manager instance
risk_manager = RiskManager()
position_monitor = PositionMonitor(risk_manager)
