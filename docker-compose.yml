version: "3.9"

services:
  solbot:
    build: .
    restart: always
    env_file: .env
    volumes:
      - ./data:/app/data
    environment:
      - PYTHONPATH=/app
    networks:
      - solbot-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  solbot-network:
    driver: bridge
