#!/usr/bin/env python3
"""
Teste básico do SOL Trading Bot
"""
import asyncio
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

async def test_imports():
    """Testar se todos os módulos podem ser importados"""
    print("🧪 Testando importações dos módulos...")
    
    try:
        import bot
        print("✅ bot.py - OK")
    except Exception as e:
        print(f"❌ bot.py - Erro: {e}")
    
    try:
        import models
        print("✅ models.py - OK")
    except Exception as e:
        print(f"❌ models.py - Erro: {e}")
    
    try:
        import db
        print("✅ db.py - OK")
    except Exception as e:
        print(f"❌ db.py - Erro: {e}")
    
    try:
        import wallet
        print("✅ wallet.py - OK")
    except Exception as e:
        print(f"❌ wallet.py - Erro: {e}")
    
    try:
        import strategy
        print("✅ strategy.py - OK")
    except Exception as e:
        print(f"❌ strategy.py - Erro: {e}")
    
    try:
        import trader
        print("✅ trader.py - OK")
    except Exception as e:
        print(f"❌ trader.py - Erro: {e}")
    
    try:
        import risk
        print("✅ risk.py - OK")
    except Exception as e:
        print(f"❌ risk.py - Erro: {e}")
    
    try:
        import scheduler
        print("✅ scheduler.py - OK")
    except Exception as e:
        print(f"❌ scheduler.py - Erro: {e}")
    
    try:
        import utils
        print("✅ utils.py - OK")
    except Exception as e:
        print(f"❌ utils.py - Erro: {e}")

async def test_database():
    """Testar inicialização do banco de dados"""
    print("\n🗄️ Testando banco de dados...")
    
    try:
        from db import init_database
        
        # Criar diretório de dados se não existir
        os.makedirs("./data", exist_ok=True)
        
        # Inicializar banco de dados
        await init_database("./data/test_solbot.db")
        print("✅ Banco de dados inicializado com sucesso")
        
        # Testar configurações
        from db import get_all_settings
        settings = await get_all_settings()
        print(f"✅ Configurações carregadas: {len(settings)} itens")
        
    except Exception as e:
        print(f"❌ Erro no banco de dados: {e}")

async def test_wallet():
    """Testar funcionalidades da carteira"""
    print("\n💰 Testando carteira...")
    
    try:
        from wallet import init_wallet_manager
        
        # Inicializar gerenciador de carteira
        wallet_mgr = init_wallet_manager(
            "./data/test_keystore.bin",
            "https://api.mainnet-beta.solana.com"
        )
        
        print("✅ Gerenciador de carteira inicializado")
        
        # Verificar se carteira existe
        exists = wallet_mgr.wallet_exists()
        print(f"✅ Verificação de carteira: {'Existe' if exists else 'Não existe'}")
        
    except Exception as e:
        print(f"❌ Erro na carteira: {e}")

async def test_strategy():
    """Testar estratégia de trading"""
    print("\n📊 Testando estratégia...")
    
    try:
        from utils import TechnicalIndicators
        
        # Testar cálculo de VWAP
        prices = [100.0, 101.0, 102.0, 103.0, 104.0]
        volumes = [1000.0, 1500.0, 1200.0, 800.0, 900.0]
        vwap = TechnicalIndicators.calculate_vwap(prices, volumes, 5)
        print(f"✅ VWAP calculado: {vwap:.4f}")
        
        # Testar cálculo de RSI
        rsi = TechnicalIndicators.calculate_rsi(prices, 4)
        print(f"✅ RSI calculado: {rsi:.2f}")
        
    except Exception as e:
        print(f"❌ Erro na estratégia: {e}")

async def test_risk_management():
    """Testar gestão de risco"""
    print("\n⚠️ Testando gestão de risco...")
    
    try:
        from risk import RiskManager
        from decimal import Decimal
        
        risk_mgr = RiskManager()
        
        # Testar cálculo de posição
        balance = Decimal("1000.0")
        price = Decimal("100.0")
        
        position = risk_mgr.calculate_position_size(balance, price)
        print(f"✅ Tamanho da posição calculado:")
        print(f"   - Notional: ${position['notional_usdc']}")
        print(f"   - SOL: {position['sol_amount']}")
        print(f"   - Risco: {position['risk_percentage']:.3%}")
        print(f"   - Alavancagem: {position['leverage_used']}x")
        
        # Testar preços de target e stop
        target, stop = risk_mgr.calculate_target_stop_prices(price, "LONG")
        print(f"✅ Preços calculados - Target: ${target}, Stop: ${stop}")
        
    except Exception as e:
        print(f"❌ Erro na gestão de risco: {e}")

async def test_configuration():
    """Testar configuração"""
    print("\n⚙️ Testando configuração...")
    
    # Verificar variáveis de ambiente
    required_vars = ["TELEGRAM_TOKEN", "TELEGRAM_ADMIN_ID", "SOLANA_RPC"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mascarar valores sensíveis
            if "TOKEN" in var:
                display_value = f"{'*' * (len(value) - 4)}{value[-4:]}"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: Não configurado")

async def main():
    """Função principal de teste"""
    print("🤖 SOL Trading Bot - Teste de Funcionalidades\n")
    
    # Executar todos os testes
    await test_imports()
    await test_configuration()
    await test_database()
    await test_wallet()
    await test_strategy()
    await test_risk_management()
    
    print("\n✅ Testes concluídos!")
    print("\n📋 Próximos passos:")
    print("1. Configure o arquivo .env com seus tokens")
    print("2. Execute: python validate_config.py")
    print("3. Execute: python -m bot")
    print("\n💡 Use /start no Telegram para ver as instruções completas!")

if __name__ == "__main__":
    asyncio.run(main())
