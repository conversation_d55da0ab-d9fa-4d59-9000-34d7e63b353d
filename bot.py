"""
Telegram bot with command handlers for SOL trading bot
"""
import os
import asyncio
from decimal import Decimal
from typing import Dict, Any
from datetime import datetime

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, 
    MessageHandler, filters, ContextTypes
)
import structlog

# Import our modules
from db import init_database, get_all_settings, set_setting, close_database
from wallet import init_wallet_manager, get_wallet_manager
from trader import init_traders
from scheduler import init_scheduler, get_scheduler
from utils import format_currency, DepositMonitor
from risk import risk_manager

logger = structlog.get_logger(__name__)


class SolTradingBot:
    """Main Telegram bot class"""
    
    def __init__(self, token: str, admin_id: int, config: Dict[str, Any]):
        self.token = token
        self.admin_id = admin_id
        self.config = config
        self.app: Application = None
        self.deposit_monitor: DepositMonitor = None
        
    async def initialize(self):
        """Initialize all bot components"""
        try:
            # Initialize database
            await init_database(self.config["DATABASE_PATH"])
            
            # Initialize wallet manager
            wallet_mgr = init_wallet_manager(
                self.config["KEYSTORE_PATH"],
                self.config["SOLANA_RPC"]
            )
            
            # Setup wallet (automatic, no password needed)
            print("🔓 Configurando carteira (sem senha)...")
            try:
                keypair, address = wallet_mgr.setup_wallet_interactive()
                print(f"✅ Carteira configurada: {address}")
                print("💡 Endereço disponível para depósitos SOL/USDC!")
            except Exception as e:
                print(f"❌ Erro ao configurar carteira: {e}")
                raise
            
            # Initialize traders
            init_traders(self.config["SOLANA_RPC"])
            
            # Initialize scheduler with notification callback
            scheduler = init_scheduler(self._send_notification)
            await scheduler.initialize(
                self.config["SOLANA_RPC"],
                self.config["SOL_PRICE_FEED"]
            )
            
            # Initialize Telegram application
            self.app = Application.builder().token(self.token).build()
            
            # Add command handlers
            self._add_handlers()
            
            logger.info("Bot initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize bot", error=str(e))
            raise
    
    def _add_handlers(self):
        """Add Telegram command handlers"""
        # Command handlers
        self.app.add_handler(CommandHandler("start", self._start_command))
        self.app.add_handler(CommandHandler("help", self._help_command))
        self.app.add_handler(CommandHandler("instrucoes", self._instrucoes_command))
        self.app.add_handler(CommandHandler("guia", self._instrucoes_command))
        self.app.add_handler(CommandHandler("status", self._status_command))
        self.app.add_handler(CommandHandler("balance", self._balance_command))
        self.app.add_handler(CommandHandler("wallet", self._wallet_command))
        self.app.add_handler(CommandHandler("deposit", self._deposit_command))
        self.app.add_handler(CommandHandler("withdraw", self._withdraw_command))
        self.app.add_handler(CommandHandler("cfg", self._config_command))
        self.app.add_handler(CommandHandler("pause", self._pause_command))
        self.app.add_handler(CommandHandler("resume", self._resume_command))
        self.app.add_handler(CommandHandler("positions", self._positions_command))
        self.app.add_handler(CommandHandler("trades", self._trades_command))
        
        # Callback query handler for inline keyboards
        self.app.add_handler(CallbackQueryHandler(self._button_callback))
        
        # Message handler for configuration updates
        self.app.add_handler(MessageHandler(
            filters.TEXT & ~filters.COMMAND, 
            self._handle_message
        ))
    
    async def _start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        if not self._is_admin(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return

        welcome_message = (
            "🤖 **SOL Trading Bot** - Bem-vindo!\n\n"
            "Este bot negocia SOL/USDC com alavancagem 5x usando estratégia VWAP + RSI.\n\n"
            "**🎯 Características Principais:**\n"
            "• Trading automatizado 24/5 (Segunda-Sexta)\n"
            "• 90% do saldo × 5x alavancagem por trade\n"
            "• Meta: +0.20%, Stop Loss: -0.15%\n"
            "• Meta diária: +0.20% (pausa automática)\n"
            "• Risco máximo diário: 1% do capital\n\n"
            "**📋 COMO USAR - PASSO A PASSO:**\n\n"
            "**1️⃣ VERIFICAR STATUS:**\n"
            "`/status` - Ver status do bot e saldos\n"
            "`/balance` - Verificar carteira\n\n"
            "**2️⃣ DEPOSITAR FUNDOS:**\n"
            "`/deposit` - Obter endereço da carteira\n"
            "• Envie SOL ou USDC para o endereço\n"
            "• Você receberá notificação automática\n\n"
            "**3️⃣ CONFIGURAR TRADING:**\n"
            "`/cfg` - Ver configurações atuais\n"
            "`/cfg set TARGET_PCT 0.003` - Meta 0.30%\n"
            "`/cfg set MAX_TRADES 2` - Máx 2 trades/dia\n"
            "`/cfg set LEVERAGE 3` - Alavancagem 3x\n\n"
            "**4️⃣ CONTROLAR TRADING:**\n"
            "`/resume` - Iniciar trading automático\n"
            "`/pause` - Pausar trading\n"
            "`/positions` - Ver posições abertas\n"
            "`/trades` - Histórico de trades\n\n"
            "**5️⃣ SACAR FUNDOS:**\n"
            "`/withdraw 10 ENDEREÇO` - Sacar fundos diretamente\n\n"
            "**⚠️ IMPORTANTE:**\n"
            "• Comece com valores pequenos para testar\n"
            "• Monitor o bot regularmente\n"
            "• Trading com alavancagem tem riscos\n"
            "• Use `/help` para ver todos os comandos\n\n"
            "**🚀 COMEÇAR AGORA:**\n"
            "1. `/deposit` - Depositar fundos\n"
            "2. `/cfg` - Verificar configurações\n"
            "3. `/resume` - Iniciar trading\n"
            "4. `/status` - Monitorar progresso"
        )

        await update.message.reply_text(welcome_message, parse_mode="Markdown")
    
    async def _help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        if not self._is_admin(update.effective_user.id):
            return

        help_text = (
            "📋 **Comandos Disponíveis:**\n\n"
            "**💰 Carteira & Saldos:**\n"
            "`/wallet` - Mostrar endereço para depósitos\n"
            "`/balance` - Mostrar saldos atuais\n"
            "`/deposit` - Instruções de depósito\n"
            "`/withdraw <valor> <endereço>` - Sacar fundos\n\n"
            "**🎯 Controle de Trading:**\n"
            "`/status` - Status do bot e estatísticas\n"
            "`/pause` - Pausar trading\n"
            "`/resume` - Retomar trading\n"
            "`/positions` - Posições abertas\n"
            "`/trades` - Histórico de trades\n\n"
            "**⚙️ Configuração:**\n"
            "`/cfg` - Ver configuração atual\n"
            "`/cfg set <CHAVE> <VALOR>` - Atualizar configuração\n\n"
            "**📊 Parâmetros Configuráveis:**\n"
            "• `TARGET_PCT` - Meta de lucro (ex: 0.003 = 0.30%)\n"
            "• `STOP_PCT` - Stop loss (ex: 0.002 = 0.20%)\n"
            "• `LEVERAGE` - Alavancagem (ex: 3 = 3x)\n"
            "• `MAX_TRADES` - Máx trades/dia (ex: 2)\n"
            "• `TRADE_PCT` - % do saldo por trade (ex: 0.80 = 80%)\n"
            "• `VWAP_PERIOD` - Período VWAP em minutos (ex: 60)\n"
            "• `RSI_PERIOD` - Período RSI (ex: 14)\n\n"
            "**💡 Exemplos de Uso:**\n"
            "`/cfg set TARGET_PCT 0.003` - Meta 0.30%\n"
            "`/cfg set LEVERAGE 3` - Alavancagem 3x\n"
            "`/cfg set MAX_TRADES 2` - Máx 2 trades/dia\n"
            "`/withdraw 10 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM`\n\n"
            "**🚨 Comandos de Emergência:**\n"
            "`/pause` - Parar trading imediatamente\n"
            "`/positions` - Ver posições para fechar manualmente\n\n"
            "**ℹ️ Informações:**\n"
            "• Bot opera 24/5 (Segunda-Sexta)\n"
            "• Estratégia: VWAP + RSI\n"
            "• Risco máximo: 1% do capital por dia\n\n"
            "**📚 Mais Ajuda:**\n"
            "• `/start` - Guia completo de início\n"
            "• `/instrucoes` - Guia rápido em português\n"
            "• `/help` - Esta lista de comandos"
        )

        await update.message.reply_text(help_text, parse_mode="Markdown")

    async def _instrucoes_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /instrucoes command - Quick guide in Portuguese"""
        if not self._is_admin(update.effective_user.id):
            return

        instrucoes_text = (
            "📚 **Guia Rápido - SOL Trading Bot**\n\n"
            "**🚀 INÍCIO RÁPIDO:**\n"
            "1. `/deposit` → Depositar SOL/USDC\n"
            "2. `/cfg set MAX_TRADES 1` → Teste com 1 trade\n"
            "3. `/resume` → Iniciar trading\n"
            "4. `/status` → Monitorar\n\n"
            "**⚙️ CONFIGURAÇÕES ESSENCIAIS:**\n"
            "• `/cfg set LEVERAGE 2` → Alavancagem 2x (mais seguro)\n"
            "• `/cfg set TARGET_PCT 0.003` → Meta 0.30%\n"
            "• `/cfg set MAX_TRADES 2` → Máx 2 trades/dia\n\n"
            "**📊 MONITORAMENTO:**\n"
            "• `/status` → Status geral e estatísticas\n"
            "• `/balance` → Saldos SOL/USDC\n"
            "• `/positions` → Posições abertas\n"
            "• `/trades` → Histórico recente\n\n"
            "**🎯 CONTROLES:**\n"
            "• `/resume` → Iniciar/retomar trading\n"
            "• `/pause` → Pausar trading\n"
            "• `/withdraw VALOR ENDEREÇO` → Sacar\n\n"
            "**⚠️ IMPORTANTE:**\n"
            "• Comece com valores pequenos (0.1 SOL)\n"
            "• Bot opera 24/5 (Segunda-Sexta)\n"
            "• Alavancagem 5x = risco alto\n"
            "• Use `/pause` para parar imediatamente\n\n"
            "**🆘 EMERGÊNCIA:**\n"
            "• `/pause` → Parar tudo\n"
            "• `/positions` → Ver posições abertas\n"
            "• `/help` → Comandos completos\n\n"
            "💡 **Dica**: Use `/start` para ver o guia completo!"
        )

        await update.message.reply_text(instrucoes_text, parse_mode="Markdown")

    async def _status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        if not self._is_admin(update.effective_user.id):
            return
        
        try:
            # Get scheduler status
            scheduler = get_scheduler()
            
            # Get current settings
            settings = await get_all_settings()
            
            # Get wallet balances
            wallet_mgr = get_wallet_manager()
            balances = await wallet_mgr.get_balances()
            
            # Get daily risk status
            risk_status = await risk_manager.check_daily_risk_limits()
            
            status_message = (
                f"🤖 **Bot Status**\n\n"
                f"**Trading:** {'🟢 Active' if settings.get('BOT_ACTIVE') == 'true' else '🔴 Paused'}\n"
                f"**Scheduler:** {'🟢 Running' if scheduler.is_running else '🔴 Stopped'}\n"
                f"**Daily Target:** {'🎯 Reached' if settings.get('DAILY_TARGET_REACHED') == 'true' else '⏳ In Progress'}\n\n"
                f"**Balances:**\n"
                f"SOL: {balances['SOL']:.4f}\n"
                f"USDC: ${balances['USDC']:,.2f}\n\n"
                f"**Daily Stats:**\n"
                f"Trades: {risk_status['trades_today']}/{risk_status['max_trades']}\n"
                f"PnL: ${risk_status['daily_pnl']:,.2f} ({risk_status['daily_pnl_pct']:+.2%})\n"
                f"Risk Limit: {risk_status['risk_limit']:.1%}\n\n"
                f"**Strategy:** VWAP + RSI\n"
                f"**Leverage:** 5x\n"
                f"**Target:** {float(settings.get('TARGET_PCT', 0.002)):.2%}\n"
                f"**Stop Loss:** {float(settings.get('STOP_PCT', 0.0015)):.2%}"
            )
            
            await update.message.reply_text(status_message, parse_mode="Markdown")
            
        except Exception as e:
            logger.error("Error in status command", error=str(e))
            await update.message.reply_text("❌ Error getting status")
    
    async def _balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /balance command"""
        if not self._is_admin(update.effective_user.id):
            return
        
        try:
            wallet_mgr = get_wallet_manager()
            balances = await wallet_mgr.get_balances()
            
            # Calculate total value (assuming SOL price)
            sol_price = 100.0  # Placeholder - would get from price feed
            total_value = balances["USDC"] + (balances["SOL"] * sol_price)
            
            balance_message = (
                f"💰 **Wallet Balances**\n\n"
                f"**SOL:** {balances['SOL']:.4f}\n"
                f"**USDC:** ${balances['USDC']:,.2f}\n\n"
                f"**Total Value:** ${total_value:,.2f}\n"
                f"**Address:** `{balances['address']}`\n\n"
                f"💡 Use /deposit to get deposit instructions"
            )
            
            await update.message.reply_text(balance_message, parse_mode="Markdown")
            
        except Exception as e:
            logger.error("Error in balance command", error=str(e))
            await update.message.reply_text("❌ Error getting balance")
    
    async def _wallet_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /wallet command - same as deposit"""
        await self._deposit_command(update, context)
    
    async def _deposit_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /deposit command"""
        if not self._is_admin(update.effective_user.id):
            return
        
        try:
            wallet_mgr = get_wallet_manager()
            address = wallet_mgr.get_wallet_address()
            
            deposit_message = (
                f"📥 **Instruções de Depósito**\n\n"
                f"**🏦 Endereço da Carteira:**\n"
                f"`{address}`\n\n"
                f"**💰 Tokens Suportados:**\n"
                f"• SOL (nativo)\n"
                f"• USDC (token SPL)\n\n"
                f"**📋 COMO DEPOSITAR:**\n"
                f"1. Copie o endereço acima\n"
                f"2. Abra sua carteira (Phantom, Solflare, etc.)\n"
                f"3. Envie SOL ou USDC para este endereço\n"
                f"4. Aguarde a confirmação (1-2 minutos)\n"
                f"5. Você receberá notificação automática\n\n"
                f"**⚠️ IMPORTANTE:**\n"
                f"• Apenas envie SOL ou USDC para este endereço\n"
                f"• Outros tokens serão perdidos\n"
                f"• Depósitos são monitorados automaticamente\n"
                f"• Comece com valores pequenos para testar\n\n"
                f"**💡 PRÓXIMOS PASSOS:**\n"
                f"• `/balance` - Verificar saldos após depósito\n"
                f"• `/cfg` - Configurar parâmetros de trading\n"
                f"• `/resume` - Iniciar trading automático\n"
                f"• `/status` - Monitorar progresso\n\n"
                f"**🔄 EXEMPLO DE VALORES PARA TESTE:**\n"
                f"• 0.1 SOL (~$10-20) para primeiros testes\n"
                f"• 10-50 USDC para trading inicial\n"
                f"• Aumente gradualmente conforme confiança"
            )
            
            # Create QR code button (optional)
            keyboard = [[
                InlineKeyboardButton("📋 Copy Address", callback_data=f"copy_{address}")
            ]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                deposit_message, 
                parse_mode="Markdown",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error("Error in deposit command", error=str(e))
            await update.message.reply_text("❌ Error getting deposit address")
    
    async def _withdraw_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /withdraw command"""
        if not self._is_admin(update.effective_user.id):
            return
        
        if len(context.args) < 2:
            await update.message.reply_text(
                "❌ Usage: /withdraw <amount> <destination_address>\n"
                "Example: `/withdraw 10 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM`",
                parse_mode="Markdown"
            )
            return
        
        try:
            amount = float(context.args[0])
            destination = context.args[1]
            
            # TODO: Implement withdrawal logic
            # This would involve creating and sending a transaction
            
            await update.message.reply_text(
                f"🚧 Withdrawal feature coming soon!\n"
                f"Amount: {amount}\n"
                f"Destination: {destination}"
            )
            
        except ValueError:
            await update.message.reply_text("❌ Invalid amount")
        except Exception as e:
            logger.error("Error in withdraw command", error=str(e))
            await update.message.reply_text("❌ Error processing withdrawal")
    
    async def _config_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /cfg command"""
        if not self._is_admin(update.effective_user.id):
            return
        
        if not context.args:
            # Show current configuration
            try:
                settings = await get_all_settings()
                
                config_message = (
                    "⚙️ **Current Configuration**\n\n"
                    f"**Trading Parameters:**\n"
                    f"• Target: {float(settings.get('TARGET_PCT', 0.002)):.2%}\n"
                    f"• Stop Loss: {float(settings.get('STOP_PCT', 0.0015)):.2%}\n"
                    f"• Trade Size: {float(settings.get('TRADE_PCT', 0.90)):.0%} of balance\n"
                    f"• Leverage: {settings.get('LEVERAGE', '5')}x\n"
                    f"• Max Trades/Day: {settings.get('MAX_TRADES', '4')}\n"
                    f"• Max Daily Risk: {float(settings.get('MAX_DD_PCT', 0.01)):.1%}\n\n"
                    f"**Strategy Parameters:**\n"
                    f"• VWAP Period: {settings.get('VWAP_PERIOD', '60')} min\n"
                    f"• RSI Period: {settings.get('RSI_PERIOD', '14')}\n"
                    f"• Slippage: {float(settings.get('SLIPPAGE_BPS', '15'))/100:.2%}\n\n"
                    f"**System:**\n"
                    f"• Check Interval: {settings.get('SCHED_INTERVAL_SEC', '60')}s\n"
                    f"• Bot Active: {settings.get('BOT_ACTIVE', 'true')}\n\n"
                    f"💡 Use `/cfg set <KEY> <VALUE>` to update settings"
                )
                
                await update.message.reply_text(config_message, parse_mode="Markdown")
                
            except Exception as e:
                logger.error("Error showing config", error=str(e))
                await update.message.reply_text("❌ Error getting configuration")
        
        elif len(context.args) >= 3 and context.args[0] == "set":
            # Update configuration
            key = context.args[1].upper()
            value = context.args[2]
            
            # Validate key
            valid_keys = {
                "TARGET_PCT", "STOP_PCT", "TRADE_PCT", "LEVERAGE", 
                "MAX_TRADES", "MAX_DD_PCT", "SLIPPAGE_BPS",
                "VWAP_PERIOD", "RSI_PERIOD", "SCHED_INTERVAL_SEC"
            }
            
            if key not in valid_keys:
                await update.message.reply_text(
                    f"❌ Invalid key. Valid keys: {', '.join(valid_keys)}"
                )
                return
            
            try:
                # Validate value based on key
                if key in ["TARGET_PCT", "STOP_PCT", "TRADE_PCT", "MAX_DD_PCT"]:
                    float(value)  # Validate it's a number
                elif key in ["LEVERAGE", "MAX_TRADES", "SLIPPAGE_BPS", "VWAP_PERIOD", "RSI_PERIOD", "SCHED_INTERVAL_SEC"]:
                    int(value)  # Validate it's an integer
                
                # Update setting
                await set_setting(key, value, f"Updated by user via Telegram")
                
                await update.message.reply_text(
                    f"✅ Updated {key} = {value}\n\n"
                    f"💡 Use /cfg to see all current settings"
                )
                
                logger.info("Setting updated via Telegram", key=key, value=value)
                
            except ValueError:
                await update.message.reply_text(f"❌ Invalid value for {key}")
            except Exception as e:
                logger.error("Error updating setting", error=str(e))
                await update.message.reply_text("❌ Error updating setting")
        
        else:
            await update.message.reply_text(
                "❌ Usage: /cfg or /cfg set <KEY> <VALUE>\n"
                "Example: `/cfg set TARGET_PCT 0.003`",
                parse_mode="Markdown"
            )

    async def _pause_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /pause command"""
        if not self._is_admin(update.effective_user.id):
            return

        try:
            scheduler = get_scheduler()
            await scheduler.pause()

            await update.message.reply_text(
                "⏸️ **Trading Paused**\n\n"
                "• No new trades will be opened\n"
                "• Existing positions continue to be monitored\n"
                "• Use /resume to restart trading\n"
                "• Use /withdraw to move funds while paused"
            )

        except Exception as e:
            logger.error("Error pausing bot", error=str(e))
            await update.message.reply_text("❌ Error pausing bot")

    async def _resume_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /resume command"""
        if not self._is_admin(update.effective_user.id):
            return

        try:
            scheduler = get_scheduler()
            await scheduler.resume()

            await update.message.reply_text(
                "▶️ **Trading Resumed**\n\n"
                "• Bot is now actively monitoring markets\n"
                "• New trades will be opened based on signals\n"
                "• Use /pause to stop trading anytime"
            )

        except Exception as e:
            logger.error("Error resuming bot", error=str(e))
            await update.message.reply_text("❌ Error resuming bot")

    async def _positions_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /positions command"""
        if not self._is_admin(update.effective_user.id):
            return

        try:
            from db import get_db_session
            from models import Trade
            from sqlmodel import select

            async for session in get_db_session():
                result = await session.exec(
                    select(Trade)
                    .where(Trade.status == "OPEN")
                    .order_by(Trade.entry_time.desc())
                )
                open_positions = result.all()

            if not open_positions:
                await update.message.reply_text("📊 No open positions")
                return

            positions_message = "📊 **Open Positions**\n\n"

            for pos in open_positions:
                # Calculate current PnL (placeholder)
                current_pnl = pos.unrealized_pnl or Decimal("0")
                pnl_emoji = "💚" if current_pnl > 0 else "❌" if current_pnl < 0 else "⚪"

                positions_message += (
                    f"{pnl_emoji} **{pos.side}** #{pos.id}\n"
                    f"Entry: ${pos.entry_price:.4f}\n"
                    f"Size: ${pos.notional_usdc:,.2f} ({pos.leverage}x)\n"
                    f"Target: ${pos.target_price:.4f}\n"
                    f"Stop: ${pos.stop_price:.4f}\n"
                    f"PnL: ${current_pnl:,.2f}\n"
                    f"Time: {pos.entry_time.strftime('%H:%M UTC')}\n\n"
                )

            await update.message.reply_text(positions_message, parse_mode="Markdown")

        except Exception as e:
            logger.error("Error getting positions", error=str(e))
            await update.message.reply_text("❌ Error getting positions")

    async def _trades_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /trades command"""
        if not self._is_admin(update.effective_user.id):
            return

        try:
            from db import get_db_session
            from models import Trade
            from sqlmodel import select
            from datetime import timedelta

            # Get trades from last 24 hours
            cutoff_time = datetime.now(datetime.UTC) - timedelta(hours=24)

            async for session in get_db_session():
                result = await session.exec(
                    select(Trade)
                    .where(Trade.entry_time >= cutoff_time)
                    .order_by(Trade.entry_time.desc())
                    .limit(10)
                )
                recent_trades = result.all()

            if not recent_trades:
                await update.message.reply_text("📈 No recent trades (last 24h)")
                return

            trades_message = "📈 **Recent Trades (24h)**\n\n"

            total_pnl = Decimal("0")

            for trade in recent_trades:
                pnl = trade.realized_pnl or Decimal("0")
                total_pnl += pnl

                status_emoji = {
                    "OPEN": "🔄",
                    "CLOSED": "✅",
                    "CANCELLED": "❌"
                }.get(trade.status, "❓")

                pnl_emoji = "💚" if pnl > 0 else "❌" if pnl < 0 else "⚪"

                trades_message += (
                    f"{status_emoji} **{trade.side}** #{trade.id}\n"
                    f"Entry: ${trade.entry_price:.4f}\n"
                    f"Exit: ${trade.exit_price:.4f}" if trade.exit_price else "Open\n"
                    f"PnL: {pnl_emoji} ${pnl:,.2f}\n"
                    f"Time: {trade.entry_time.strftime('%m/%d %H:%M')}\n\n"
                )

            trades_message += f"**Total PnL (24h):** ${total_pnl:,.2f}"

            await update.message.reply_text(trades_message, parse_mode="Markdown")

        except Exception as e:
            logger.error("Error getting trades", error=str(e))
            await update.message.reply_text("❌ Error getting trades")

    async def _button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button callbacks"""
        query = update.callback_query
        await query.answer()

        if query.data.startswith("copy_"):
            address = query.data[5:]  # Remove "copy_" prefix
            await query.edit_message_text(
                f"📋 **Address Copied**\n\n`{address}`\n\n"
                f"💡 Tap and hold the address above to copy it",
                parse_mode="Markdown"
            )

    async def _handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle non-command messages"""
        if not self._is_admin(update.effective_user.id):
            return

        # For now, just ignore non-command messages
        # Could be used for interactive configuration in the future
        pass

    def _is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        return user_id == self.admin_id

    async def _send_notification(self, message: str):
        """Send notification to admin"""
        try:
            if self.app:
                await self.app.bot.send_message(
                    chat_id=self.admin_id,
                    text=message,
                    parse_mode="Markdown"
                )
                logger.info("Notification sent successfully")
        except Exception as e:
            logger.error("Failed to send notification", error=str(e), admin_id=self.admin_id)
            print(f"⚠️ Não foi possível enviar notificação para o Telegram (ID: {self.admin_id})")
            print(f"   Verifique se o TELEGRAM_ADMIN_ID está correto no arquivo .env")
            print(f"   Mensagem que seria enviada: {message[:100]}...")

    async def start_bot(self):
        """Start the Telegram bot"""
        try:
            # Start scheduler
            scheduler = get_scheduler()
            await scheduler.start()

            # Start deposit monitoring
            wallet_mgr = get_wallet_manager()
            self.deposit_monitor = DepositMonitor(
                wallet_mgr.get_wallet_address(),
                self.config["SOLANA_RPC"],
                self._handle_deposit
            )

            # Start deposit monitoring in background
            import asyncio as async_lib
            async_lib.create_task(self.deposit_monitor.start_monitoring())

            # Start Telegram bot
            await self.app.initialize()
            await self.app.start()
            await self.app.updater.start_polling()

            logger.info("Telegram bot started successfully")

            # Send startup notification with complete instructions
            startup_message = (
                "🚀 **SOL Trading Bot Iniciado com Sucesso!**\n\n"
                "**📋 PRIMEIROS PASSOS:**\n\n"
                "**1️⃣ VERIFICAR STATUS:**\n"
                "`/status` - Ver status atual do bot\n"
                "`/balance` - Verificar saldos da carteira\n\n"
                "**2️⃣ DEPOSITAR FUNDOS:**\n"
                "`/deposit` - Obter endereço da carteira\n"
                "• Envie SOL ou USDC para começar\n"
                "• Comece com valores pequenos para testar\n\n"
                "**3️⃣ CONFIGURAR (OPCIONAL):**\n"
                "`/cfg` - Ver configurações atuais\n"
                "`/cfg set MAX_TRADES 1` - Limitar a 1 trade para teste\n"
                "`/cfg set TARGET_PCT 0.003` - Meta de 0.30%\n\n"
                "**4️⃣ INICIAR TRADING:**\n"
                "`/resume` - Ativar trading automático\n"
                "`/positions` - Monitorar posições\n"
                "`/trades` - Ver histórico\n\n"
                "**⚠️ IMPORTANTE:**\n"
                "• Bot opera 24/5 (Segunda-Sexta)\n"
                "• Alavancagem 5x ativa por padrão\n"
                "• Risco máximo: 1% do capital/dia\n"
                "• Use `/pause` para parar a qualquer momento\n\n"
                "**🆘 AJUDA:**\n"
                "`/help` - Lista completa de comandos\n"
                "`/instrucoes` - Guia rápido em português\n"
                "`/start` - Ver este guia novamente\n\n"
                "**🎯 ESTRATÉGIA:**\n"
                "• VWAP + RSI para sinais de entrada\n"
                "• Meta: +0.20% por trade\n"
                "• Stop Loss: -0.15% por trade\n"
                "• 90% do saldo × 5x alavancagem\n\n"
                "✅ Bot está monitorando os mercados!\n"
                "💡 Digite `/deposit` para começar"
            )

            await self._send_notification(startup_message)

            # Keep the bot running
            try:
                await self.app.updater.idle()
            except AttributeError:
                # For newer versions of python-telegram-bot
                import signal
                import asyncio

                # Create a future that will be set when we receive a signal
                stop_signals = (signal.SIGHUP, signal.SIGTERM, signal.SIGINT)
                loop = asyncio.get_running_loop()

                def signal_handler():
                    for s in stop_signals:
                        loop.remove_signal_handler(s)
                    loop.stop()

                for s in stop_signals:
                    loop.add_signal_handler(s, signal_handler)

                # Keep running until stopped
                try:
                    await asyncio.Future()  # Run forever
                except asyncio.CancelledError:
                    pass

        except Exception as e:
            logger.error("Error starting bot", error=str(e))
            raise

    async def stop_bot(self):
        """Stop the Telegram bot"""
        try:
            # Stop scheduler
            scheduler = get_scheduler()
            await scheduler.stop()

            # Stop deposit monitoring
            if self.deposit_monitor:
                self.deposit_monitor.stop_monitoring()

            # Stop Telegram bot
            if self.app:
                await self.app.updater.stop()
                await self.app.stop()
                await self.app.shutdown()

            # Close database
            await close_database()

            logger.info("Bot stopped successfully")

        except Exception as e:
            logger.error("Error stopping bot", error=str(e))

    async def _handle_deposit(self, token: str, amount: float, new_balance: float):
        """Handle deposit notification"""
        message = (
            f"💰 **Deposit Detected**\n\n"
            f"Token: {token}\n"
            f"Amount: {format_currency(amount, token)}\n"
            f"New Balance: {format_currency(new_balance, token)}\n\n"
            f"✅ Funds are now available for trading"
        )
        await self._send_notification(message)


async def main():
    """Main entry point"""
    import structlog
    from dotenv import load_dotenv

    # Load environment variables
    load_dotenv()

    # Configure logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Get configuration from environment
    config = {
        "DATABASE_PATH": os.getenv("DATABASE_PATH", "./data/solbot.db"),
        "KEYSTORE_PATH": os.getenv("KEYSTORE_PATH", "./data/keystore.bin"),
        "SOLANA_RPC": os.getenv("SOLANA_RPC", "https://api.mainnet-beta.solana.com"),
        "SOL_PRICE_FEED": os.getenv("SOL_PRICE_FEED", "H6ARHf6YXhGYeQfUzQNGk6rDNnLBQKrenN712K4AQJEG"),
    }

    # Get Telegram configuration
    telegram_token = os.getenv("TELEGRAM_TOKEN")
    admin_id_str = os.getenv("TELEGRAM_ADMIN_ID")

    if not telegram_token or not admin_id_str:
        print("❌ Missing TELEGRAM_TOKEN or TELEGRAM_ADMIN_ID environment variables")
        return

    try:
        admin_id = abs(int(admin_id_str))  # Garantir que seja positivo
    except ValueError:
        print(f"❌ Invalid TELEGRAM_ADMIN_ID: {admin_id_str}")
        return

    print(f"🤖 Starting SOL Trading Bot...")
    print(f"📱 Telegram Token: ...{telegram_token[-8:]}")
    print(f"👤 Admin ID: {admin_id}")

    # Create and start bot
    bot = SolTradingBot(telegram_token, admin_id, config)

    try:
        print("🔧 Initializing bot...")
        await bot.initialize()
        print("🚀 Starting bot...")
        await bot.start_bot()
    except KeyboardInterrupt:
        print("\n⏹️ Received shutdown signal")
    except Exception as e:
        print(f"❌ Bot crashed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🛑 Stopping bot...")
        await bot.stop_bot()


if __name__ == "__main__":
    asyncio.run(main())
