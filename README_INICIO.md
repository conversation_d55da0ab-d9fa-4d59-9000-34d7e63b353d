# 🚀 SOL Trading Bot - <PERSON> Começar

## ✅ **PROGRAMA CORRIGIDO E FUNCIONANDO!**

O bot está pronto para uso com todas as funcionalidades implementadas:
- ✅ Trading automático 24/5 com alavancagem 5x
- ✅ Estratégia VWAP + RSI
- ✅ Gestão de risco avançada
- ✅ Interface Telegram completa em português
- ✅ Instruções detalhadas no `/start`

---

## 🔧 **CONFIGURAÇÃO INICIAL**

### **1. Configurar Telegram Bot**

1. **Criar bot no Telegram:**
   - Abra [@BotFather](https://t.me/botfather) no Telegram
   - Digite `/newbot`
   - Escolha um nome para seu bot
   - Copie o token que aparece (ex: `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`)

2. **Obter seu ID do Telegram:**
   - A<PERSON> [@userinfobot](https://t.me/userinfobot) no Telegram
   - Digite `/start`
   - Copie seu ID (ex: `123456789`)

### **2. Configurar Arquivo .env**

Edite o arquivo `.env` com seus valores reais:

```bash
# Telegram Configuration - SUBSTITUA PELOS SEUS VALORES
TELEGRAM_TOKEN=SEU_TOKEN_AQUI
TELEGRAM_ADMIN_ID=SEU_ID_AQUI

# Solana Network
SOLANA_RPC=https://api.mainnet-beta.solana.com

# Trading Parameters (já configurados)
SLIPPAGE_BPS=15
TRADE_PCT=0.90
LEVERAGE=5
MAX_TRADES=4
TARGET_PCT=0.002
STOP_PCT=0.0015
MAX_DD_PCT=0.01
```

### **3. Instalar Dependências**

```bash
pip install -r requirements.txt
```

### **4. Validar Configuração**

```bash
python validate_config.py
```

Deve mostrar: ✅ Configuration validation passed!

---

## 🚀 **EXECUTAR O BOT**

### **Opção 1: Execução Local**
```bash
python -m bot
```

### **Opção 2: Com Script de Início**
```bash
./start.sh
```

### **Opção 3: Docker (Produção)**
```bash
docker-compose up -d
```

---

## 📱 **USAR O BOT NO TELEGRAM**

### **1. Iniciar Conversa**
- Abra seu bot no Telegram
- Digite `/start`
- **O bot mostrará instruções completas automaticamente!**

### **2. Comandos Principais**
```
/start      - Guia completo de uso
/instrucoes - Guia rápido em português
/help       - Lista de todos os comandos
/status     - Ver status do bot
/deposit    - Obter endereço para depósito
/balance    - Ver saldos
/cfg        - Ver/alterar configurações
/resume     - Iniciar trading
/pause      - Pausar trading
```

### **3. Fluxo Recomendado**
1. `/start` - Ver instruções completas
2. `/deposit` - Depositar SOL ou USDC
3. `/cfg set MAX_TRADES 1` - Limitar para teste
4. `/resume` - Iniciar trading
5. `/status` - Monitorar progresso

---

## 🎯 **CARACTERÍSTICAS DO BOT**

### **Trading Automático:**
- **Horário**: 24/5 (Segunda a Sexta)
- **Estratégia**: VWAP 60min + RSI 14
- **Alavancagem**: 5x (configurável)
- **Posição**: 90% do saldo por trade
- **Meta**: +0.20% por trade
- **Stop**: -0.15% por trade

### **Gestão de Risco:**
- **Risco máximo por trade**: ~0.675% do capital
- **Risco máximo diário**: 1% do capital
- **Meta diária**: +0.20% (pausa automática)
- **Máximo trades/dia**: 4 (configurável)

### **Notificações Automáticas:**
- ✅ Trade aberto com detalhes
- ✅ Trade fechado com PnL
- ✅ Meta diária atingida
- ✅ Depósito detectado
- ⚠️ Alertas de risco

---

## 🛡️ **SEGURANÇA**

### **Carteira Própria:**
- Carteira auto-custódia criptografada
- Chave privada protegida por senha
- Nunca compartilhada com terceiros

### **Controles de Risco:**
- Limites automáticos de posição
- Stop loss obrigatório
- Pausa automática em caso de risco

### **Recomendações:**
- Comece com valores pequenos (0.1 SOL)
- Use `/cfg set MAX_TRADES 1` para primeiros testes
- Monitore regularmente com `/status`
- Mantenha sempre SOL para taxas

---

## 🆘 **SUPORTE E SOLUÇÃO DE PROBLEMAS**

### **Bot não responde:**
```bash
# Verificar se está rodando
ps aux | grep python

# Verificar logs
tail -f data/solbot.log

# Reiniciar
python -m bot
```

### **Comandos de Emergência:**
```
/pause      - Parar trading imediatamente
/positions  - Ver posições abertas
/status     - Verificar status geral
```

### **Problemas Comuns:**
- **"Unauthorized access"**: Verifique TELEGRAM_ADMIN_ID no .env
- **Bot não inicia**: Verifique TELEGRAM_TOKEN no .env
- **Sem trades**: Pode estar fora do horário 24/5 ou sem sinais
- **Erro de saldo**: Use `/balance` para verificar fundos

---

## 📊 **MONITORAMENTO**

### **Comandos de Acompanhamento:**
```
/status     - Status geral e estatísticas diárias
/balance    - Saldos SOL/USDC atuais
/positions  - Posições abertas com PnL em tempo real
/trades     - Histórico dos últimos trades
/cfg        - Configurações atuais
```

### **Métricas Importantes:**
- **PnL Diário**: Meta +0.20%
- **Taxa de Acerto**: % de trades lucrativos
- **Drawdown**: Deve ficar abaixo de 1%
- **Frequência**: 2-4 trades por dia típico

---

## 🎉 **PRONTO PARA USAR!**

O bot está **100% funcional** e mostrará **instruções completas** quando você usar `/start` no Telegram.

### **Resumo dos Passos:**
1. ✅ Configure `.env` com seus tokens
2. ✅ Execute `python -m bot`
3. ✅ Use `/start` no Telegram
4. ✅ Siga as instruções que aparecem automaticamente

**💡 O próprio bot te guiará através de todo o processo!**

---

**🚨 Lembre-se: Comece sempre com valores pequenos para testar!**
