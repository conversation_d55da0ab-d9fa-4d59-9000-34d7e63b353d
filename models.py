"""
SQLModel classes for database schema
"""
from datetime import datetime
from decimal import Decimal
from typing import Optional
from sqlmodel import SQLModel, Field, Column, DateTime
from sqlalchemy import func


class Trade(SQLModel, table=True):
    """Trade execution record"""
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Trade identification
    signature: str = Field(index=True, unique=True)
    side: str = Field(index=True)  # "LONG" or "SHORT"
    
    # Trade details
    entry_price: Decimal
    exit_price: Optional[Decimal] = Field(default=None)
    size_sol: Decimal  # SOL amount
    notional_usdc: Decimal  # USDC notional
    leverage: Decimal = Field(default=Decimal("5.0"))
    
    # PnL tracking
    realized_pnl: Optional[Decimal] = Field(default=None)
    unrealized_pnl: Optional[Decimal] = Field(default=None)
    fees_paid: Decimal = Field(default=Decimal("0"))
    
    # Status and timing
    status: str = Field(default="OPEN")  # OPEN, CLOSED, CANCELLED
    entry_time: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    exit_time: Optional[datetime] = Field(default=None)
    
    # Risk management
    target_price: Optional[Decimal] = Field(default=None)
    stop_price: Optional[Decimal] = Field(default=None)
    
    # Metadata
    strategy_signal: str = Field(default="")  # Signal that triggered trade
    notes: Optional[str] = Field(default=None)


class Settings(SQLModel, table=True):
    """Bot configuration settings"""
    id: Optional[int] = Field(default=None, primary_key=True)
    key: str = Field(unique=True, index=True)
    value: str
    description: Optional[str] = Field(default=None)
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )


class Balance(SQLModel, table=True):
    """Balance snapshots for tracking"""
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Balance details
    sol_balance: Decimal
    usdc_balance: Decimal
    total_value_usdc: Decimal

    # Daily tracking
    daily_pnl: Decimal = Field(default=Decimal("0"))
    daily_trades: int = Field(default=0)
    max_drawdown: Decimal = Field(default=Decimal("0"))
    
    # Timestamp
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    
    # Metadata
    notes: Optional[str] = Field(default=None)


class PriceData(SQLModel, table=True):
    """Price data for strategy calculations"""
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # Price information
    symbol: str = Field(index=True)  # "SOL/USDC"
    price: Decimal
    volume: Optional[Decimal] = Field(default=None)

    # Technical indicators
    vwap: Optional[Decimal] = Field(default=None)
    rsi: Optional[Decimal] = Field(default=None)
    
    # Timestamp
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    
    # Source
    source: str = Field(default="pyth")  # pyth, jupiter, etc.


# Default settings for initialization
DEFAULT_SETTINGS = {
    "TARGET_PCT": "0.002",
    "STOP_PCT": "0.0015", 
    "TRADE_PCT": "0.90",
    "LEVERAGE": "5",
    "MAX_TRADES": "4",
    "MAX_DD_PCT": "0.01",
    "SLIPPAGE_BPS": "15",
    "SCHED_INTERVAL_SEC": "60",
    "VWAP_PERIOD": "60",
    "RSI_PERIOD": "14",
    "BOT_ACTIVE": "true",
    "DAILY_TARGET_REACHED": "false",
    "LAST_RESET_DATE": "",
}
