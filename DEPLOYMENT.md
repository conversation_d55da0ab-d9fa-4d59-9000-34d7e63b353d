# SOL Trading Bot - Deployment Guide

## 🚀 Quick Start

### 1. Prerequisites Setup

**Required:**
- Python 3.11+ (tested with 3.12.4)
- Telegram <PERSON><PERSON> from [@BotFather](https://t.me/botfather)
- Your Telegram User ID (get from [@userinfobot](https://t.me/userinfobot))
- Solana RPC endpoint (mainnet or devnet)

**Optional:**
- Docker & Docker Compose for production deployment

### 2. Environment Configuration

1. **Copy environment template:**
```bash
cp .env.example .env
```

2. **Edit `.env` with your values:**
```bash
# Required - Get from @BotFather
TELEGRAM_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# Required - Get from @userinfobot  
TELEGRAM_ADMIN_ID=123456789

# Required - Solana RPC endpoint
SOLANA_RPC=https://api.mainnet-beta.solana.com

# Optional - Customize trading parameters
LEVERAGE=5
TARGET_PCT=0.002
STOP_PCT=0.0015
TRADE_PCT=0.90
MAX_TRADES=4
```

3. **Validate configuration:**
```bash
python validate_config.py
```

### 3. Deployment Options

#### Option A: Local Development
```bash
# Make startup script executable
chmod +x start.sh

# Start the bot
./start.sh
```

#### Option B: Docker Production
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f solbot

# Stop
docker-compose down
```

## 🔧 Configuration Reference

### Trading Parameters

| Parameter | Default | Description | Range |
|-----------|---------|-------------|-------|
| `TARGET_PCT` | 0.002 | Target profit per trade (0.20%) | 0.001-0.01 |
| `STOP_PCT` | 0.0015 | Stop loss per trade (0.15%) | 0.0005-0.005 |
| `TRADE_PCT` | 0.90 | Percentage of balance per trade | 0.1-1.0 |
| `LEVERAGE` | 5 | Leverage multiplier | 1-10 |
| `MAX_TRADES` | 4 | Maximum trades per day | 1-20 |
| `MAX_DD_PCT` | 0.01 | Maximum daily drawdown (1%) | 0.005-0.05 |
| `SLIPPAGE_BPS` | 15 | Slippage tolerance (0.15%) | 1-100 |

### Strategy Parameters

| Parameter | Default | Description | Range |
|-----------|---------|-------------|-------|
| `VWAP_PERIOD` | 60 | VWAP calculation period (minutes) | 10-120 |
| `RSI_PERIOD` | 14 | RSI calculation period | 5-30 |
| `SCHED_INTERVAL_SEC` | 60 | Trading cycle interval (seconds) | 30-300 |

### System Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `DATABASE_PATH` | ./data/solbot.db | SQLite database location |
| `KEYSTORE_PATH` | ./data/keystore.bin | Encrypted wallet location |
| `LOG_LEVEL` | INFO | Logging level |

## 🔐 Security Setup

### 1. Wallet Creation
On first run, the bot will prompt for wallet creation:
```
No wallet found, creating new wallet...
Enter new wallet password (min 12 chars): ********
Confirm password: ********
New wallet created: 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
```

**Important:**
- Use a strong password (12+ characters)
- Store the password securely
- The wallet address will be shown for deposits

### 2. Initial Funding
1. Send SOL or USDC to the displayed wallet address
2. Use `/balance` command to verify funds received
3. Start with small amounts for testing

### 3. Security Best Practices
- Never share your `.env` file
- Use strong passwords for wallet encryption
- Monitor the bot regularly
- Start with small amounts
- Keep backups of your keystore file

## 📱 First Steps After Deployment

### 1. Verify Bot Status
```
/start    # Welcome message
/status   # Check bot status
/balance  # Verify wallet connection
```

### 2. Configure Trading
```
/cfg                        # View current settings
/cfg set TARGET_PCT 0.003   # Set 0.30% target
/cfg set MAX_TRADES 2       # Limit to 2 trades/day
```

### 3. Fund the Wallet
```
/deposit  # Get wallet address
# Send SOL or USDC to the address
/balance  # Verify funds received
```

### 4. Start Trading
```
/resume   # Start automated trading
/status   # Monitor progress
```

## 🔍 Monitoring & Maintenance

### Health Checks
```bash
# Check bot status
curl -f http://localhost:8080/health || echo "Bot may be down"

# View recent logs
docker-compose logs --tail=50 solbot

# Check database
sqlite3 data/solbot.db "SELECT COUNT(*) FROM trades;"
```

### Daily Monitoring
- Check `/status` for daily performance
- Monitor `/positions` for open trades
- Review `/trades` for recent activity
- Verify balance with `/balance`

### Troubleshooting
1. **Bot not responding:**
   - Check logs: `docker-compose logs solbot`
   - Verify Telegram token in `.env`
   - Restart: `docker-compose restart solbot`

2. **No trades executing:**
   - Check `/status` - may be paused
   - Verify market hours (24/5 Mon-Fri)
   - Check balance with `/balance`
   - Review strategy signals in logs

3. **Wallet issues:**
   - Verify RPC endpoint is working
   - Check wallet balance on Solscan
   - Ensure sufficient SOL for transaction fees

## 📊 Performance Monitoring

### Key Metrics to Track
- **Daily PnL**: Target +0.20% per day
- **Win Rate**: Percentage of profitable trades
- **Max Drawdown**: Should stay below 1%
- **Trade Frequency**: 2-4 trades per day typical

### Telegram Commands for Monitoring
```
/status     # Overall bot health
/positions  # Current open positions
/trades     # Recent trade history
/balance    # Current wallet balances
/cfg        # Current configuration
```

## 🚨 Risk Management

### Built-in Protections
- **Position Sizing**: Max 90% of balance per trade
- **Leverage Limit**: 5x maximum exposure
- **Stop Loss**: Automatic -0.15% exit
- **Daily Limit**: Auto-pause at +0.20% daily profit
- **Risk Cap**: 1% maximum daily drawdown

### Manual Controls
- `/pause` - Stop new trades immediately
- `/resume` - Restart trading
- `/withdraw` - Move funds (requires pause)

### Emergency Procedures
1. **Immediate Stop**: `/pause`
2. **Check Positions**: `/positions`
3. **Review Logs**: `docker-compose logs solbot`
4. **Manual Exit**: Close positions via Jupiter directly if needed

## 🔄 Updates & Maintenance

### Configuration Updates
Most settings can be updated without restart:
```
/cfg set LEVERAGE 3        # Change leverage
/cfg set MAX_TRADES 6      # Increase daily limit
/cfg set TARGET_PCT 0.003  # Raise profit target
```

### Code Updates
For code changes:
```bash
# Stop bot
docker-compose down

# Pull updates
git pull

# Rebuild and restart
docker-compose up -d --build
```

### Database Maintenance
```bash
# Backup database
cp data/solbot.db data/solbot_backup_$(date +%Y%m%d).db

# Check database size
ls -lh data/solbot.db

# Vacuum database (monthly)
sqlite3 data/solbot.db "VACUUM;"
```

---

## 📞 Support

For issues:
1. Check logs first: `docker-compose logs solbot`
2. Validate config: `python validate_config.py`
3. Test basic commands: `/status`, `/balance`
4. Review this deployment guide

**Remember**: Start with small amounts and monitor closely!
