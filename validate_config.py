#!/usr/bin/env python3
"""
Configuration validation script for SOL Trading Bot
"""
import os
import sys
from decimal import Decimal
from dotenv import load_dotenv


def validate_config():
    """Validate configuration and environment setup"""
    print("🔍 Validating SOL Trading Bot configuration...")
    
    # Load environment variables
    load_dotenv()
    
    errors = []
    warnings = []
    
    # Required environment variables
    required_vars = {
        "TELEGRAM_TOKEN": "Telegram bot token",
        "TELEGRAM_ADMIN_ID": "Telegram admin user ID",
        "SOLANA_RPC": "Solana RPC endpoint",
    }
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            errors.append(f"❌ Missing {var} ({description})")
        else:
            print(f"✅ {var}: {'*' * (len(value) - 4) + value[-4:]}")
    
    # Validate Telegram Admin ID
    admin_id = os.getenv("TELEGRAM_ADMIN_ID")
    if admin_id:
        try:
            int(admin_id)
        except ValueError:
            errors.append("❌ TELEGRAM_ADMIN_ID must be a valid integer")
    
    # Validate trading parameters
    trading_params = {
        "SLIPPAGE_BPS": (1, 100, "Slippage in basis points"),
        "TRADE_PCT": (0.1, 1.0, "Trade percentage of balance"),
        "LEVERAGE": (1, 10, "Leverage multiplier"),
        "MAX_TRADES": (1, 20, "Maximum trades per day"),
        "TARGET_PCT": (0.001, 0.01, "Target percentage"),
        "STOP_PCT": (0.0005, 0.005, "Stop loss percentage"),
        "MAX_DD_PCT": (0.005, 0.05, "Maximum daily drawdown"),
    }
    
    for param, (min_val, max_val, description) in trading_params.items():
        value_str = os.getenv(param)
        if value_str:
            try:
                value = float(value_str)
                if not (min_val <= value <= max_val):
                    warnings.append(f"⚠️  {param} ({value}) outside recommended range [{min_val}, {max_val}]")
                else:
                    print(f"✅ {param}: {value} ({description})")
            except ValueError:
                errors.append(f"❌ {param} must be a valid number")
        else:
            warnings.append(f"⚠️  {param} not set, will use default")
    
    # Validate strategy parameters
    strategy_params = {
        "VWAP_PERIOD": (10, 120, "VWAP period in minutes"),
        "RSI_PERIOD": (5, 30, "RSI calculation period"),
        "SCHED_INTERVAL_SEC": (30, 300, "Scheduler interval in seconds"),
    }
    
    for param, (min_val, max_val, description) in strategy_params.items():
        value_str = os.getenv(param)
        if value_str:
            try:
                value = int(value_str)
                if not (min_val <= value <= max_val):
                    warnings.append(f"⚠️  {param} ({value}) outside recommended range [{min_val}, {max_val}]")
                else:
                    print(f"✅ {param}: {value} ({description})")
            except ValueError:
                errors.append(f"❌ {param} must be a valid integer")
    
    # Validate file paths
    paths = {
        "DATABASE_PATH": os.getenv("DATABASE_PATH", "./data/solbot.db"),
        "KEYSTORE_PATH": os.getenv("KEYSTORE_PATH", "./data/keystore.bin"),
    }
    
    for path_name, path_value in paths.items():
        directory = os.path.dirname(path_value)
        if directory and not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ Created directory for {path_name}: {directory}")
            except Exception as e:
                errors.append(f"❌ Cannot create directory for {path_name}: {e}")
        else:
            print(f"✅ {path_name}: {path_value}")
    
    # Calculate risk metrics
    try:
        trade_pct = float(os.getenv("TRADE_PCT", "0.90"))
        leverage = float(os.getenv("LEVERAGE", "5"))
        stop_pct = float(os.getenv("STOP_PCT", "0.0015"))
        max_dd_pct = float(os.getenv("MAX_DD_PCT", "0.01"))
        
        # Calculate maximum loss per trade
        max_loss_per_trade = trade_pct * leverage * stop_pct
        
        print(f"\n📊 Risk Analysis:")
        print(f"   Trade size: {trade_pct:.1%} of balance")
        print(f"   Leverage: {leverage}x")
        print(f"   Stop loss: {stop_pct:.2%}")
        print(f"   Max loss per trade: {max_loss_per_trade:.3%} of capital")
        print(f"   Daily risk limit: {max_dd_pct:.1%}")
        
        if max_loss_per_trade > max_dd_pct:
            errors.append(f"❌ Max loss per trade ({max_loss_per_trade:.3%}) exceeds daily risk limit ({max_dd_pct:.1%})")
        else:
            print(f"✅ Risk parameters are within limits")
            
    except Exception as e:
        errors.append(f"❌ Error calculating risk metrics: {e}")
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 11):
        warnings.append(f"⚠️  Python {python_version.major}.{python_version.minor} detected. Python 3.11+ recommended.")
    else:
        print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Summary
    print(f"\n📋 Validation Summary:")
    print(f"   Errors: {len(errors)}")
    print(f"   Warnings: {len(warnings)}")
    
    if errors:
        print(f"\n❌ Configuration Errors:")
        for error in errors:
            print(f"   {error}")
    
    if warnings:
        print(f"\n⚠️  Configuration Warnings:")
        for warning in warnings:
            print(f"   {warning}")
    
    if not errors:
        print(f"\n✅ Configuration validation passed!")
        print(f"   Ready to start the SOL Trading Bot")
        return True
    else:
        print(f"\n❌ Configuration validation failed!")
        print(f"   Please fix the errors above before starting the bot")
        return False


if __name__ == "__main__":
    success = validate_config()
    sys.exit(0 if success else 1)
