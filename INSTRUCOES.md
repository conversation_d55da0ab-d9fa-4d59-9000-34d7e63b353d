# 🤖 SOL Trading Bot - Instruções de Uso

## 🚀 Como Usar o Bot - Guia <PERSON>

### 📱 **COMANDOS PRINCIPAIS**

#### **1️⃣ PRIMEIROS PASSOS**
```
/start    - Iniciar bot e ver instruções
/help     - Lista completa de comandos
/status   - Ver status atual do bot
/balance  - Verificar saldos da carteira
```

#### **2️⃣ DEPOSITAR FUNDOS**
```
/deposit  - Obter endereço da carteira
/wallet   - Mesmo que /deposit
```

**Como depositar:**
1. Use `/deposit` para obter o endereço
2. Envie SOL ou USDC da sua carteira
3. Aguarde confirmação (1-2 minutos)
4. Receberá notificação automática

#### **3️⃣ CONTROLAR TRADING**
```
/resume     - Iniciar trading automático
/pause      - Pausar trading
/positions  - Ver posições abertas
/trades     - Histórico de trades
```

#### **4️⃣ CONFIGURAÇÕES**
```
/cfg                        - Ver configurações atuais
/cfg set TARGET_PCT 0.003   - Meta de lucro 0.30%
/cfg set LEVERAGE 3         - Alavancagem 3x
/cfg set MAX_TRADES 2       - Máximo 2 trades por dia
```

#### **5️⃣ SACAR FUNDOS**
```
/pause                                    - Pausar bot primeiro
/withdraw 10 9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
```

---

## ⚙️ **CONFIGURAÇÕES IMPORTANTES**

### **Parâmetros Principais:**
- **TARGET_PCT**: Meta de lucro por trade (padrão: 0.002 = 0.20%)
- **STOP_PCT**: Stop loss por trade (padrão: 0.0015 = 0.15%)
- **LEVERAGE**: Alavancagem (padrão: 5x)
- **TRADE_PCT**: % do saldo usado por trade (padrão: 0.90 = 90%)
- **MAX_TRADES**: Máximo trades por dia (padrão: 4)

### **Exemplos de Configuração:**
```bash
# Para trading mais conservador
/cfg set LEVERAGE 2
/cfg set MAX_TRADES 2
/cfg set TARGET_PCT 0.0025

# Para trading mais agressivo
/cfg set LEVERAGE 5
/cfg set MAX_TRADES 6
/cfg set TARGET_PCT 0.002
```

---

## 📊 **COMO FUNCIONA**

### **Estratégia de Trading:**
- **Indicadores**: VWAP (60 min) + RSI (14 períodos)
- **Sinal LONG**: Preço < VWAP × 0.9975 E RSI < 30
- **Sinal SHORT**: Preço > VWAP × 1.0025 E RSI > 70
- **Horário**: 24/5 (Segunda a Sexta)

### **Gestão de Risco:**
- **Tamanho da posição**: 90% do saldo × alavancagem
- **Perda máxima por trade**: ~0.675% do capital
- **Risco máximo diário**: 1% do capital
- **Meta diária**: +0.20% (pausa automática)

---

## 🎯 **FLUXO DE USO RECOMENDADO**

### **Para Iniciantes:**
1. **Configurar para teste:**
   ```
   /cfg set MAX_TRADES 1
   /cfg set LEVERAGE 2
   /cfg set TARGET_PCT 0.005
   ```

2. **Depositar valor pequeno:**
   - 0.1 SOL ou 10-20 USDC para teste

3. **Iniciar trading:**
   ```
   /resume
   /status
   ```

4. **Monitorar:**
   ```
   /positions  - Ver trades abertos
   /trades     - Ver histórico
   ```

### **Para Usuários Experientes:**
1. **Configurar parâmetros desejados:**
   ```
   /cfg set LEVERAGE 5
   /cfg set MAX_TRADES 4
   /cfg set TARGET_PCT 0.002
   ```

2. **Depositar capital de trading**

3. **Ativar e monitorar:**
   ```
   /resume
   /status
   ```

---

## 🚨 **COMANDOS DE EMERGÊNCIA**

### **Parar Trading Imediatamente:**
```
/pause
```

### **Ver Posições Abertas:**
```
/positions
```

### **Sacar Fundos:**
```
/pause
/withdraw VALOR ENDEREÇO
```

---

## 💡 **DICAS IMPORTANTES**

### **✅ Boas Práticas:**
- Comece sempre com valores pequenos
- Configure `MAX_TRADES 1` para primeiros testes
- Use `/status` regularmente para monitorar
- Mantenha sempre SOL para taxas de transação
- Monitore o bot especialmente nos primeiros dias

### **⚠️ Cuidados:**
- Trading com alavancagem tem riscos altos
- Nunca invista mais do que pode perder
- Bot opera automaticamente 24/5
- Mercados cripto são voláteis
- Sempre teste com valores pequenos primeiro

### **🔧 Solução de Problemas:**
- **Bot não responde**: Verifique se está ativo com `/status`
- **Sem trades**: Pode estar fora do horário (24/5) ou sem sinais
- **Erro de saldo**: Verifique com `/balance`
- **Posição travada**: Use `/pause` e `/positions`

---

## 📈 **MONITORAMENTO**

### **Comandos de Monitoramento:**
```
/status     - Status geral e estatísticas diárias
/balance    - Saldos atuais SOL/USDC
/positions  - Posições abertas com PnL
/trades     - Últimos trades (24h)
/cfg        - Configurações atuais
```

### **Notificações Automáticas:**
- ✅ Trade aberto (com detalhes da posição)
- ✅ Trade fechado (com PnL)
- ✅ Meta diária atingida
- ✅ Depósito detectado
- ⚠️ Limite de risco atingido

---

## 🆘 **SUPORTE**

### **Verificações Básicas:**
1. `/status` - Ver se bot está ativo
2. `/balance` - Verificar saldos
3. `/cfg` - Ver configurações
4. `/help` - Lista de comandos

### **Em Caso de Problemas:**
1. Use `/pause` para parar trading
2. Verifique `/positions` para posições abertas
3. Use `/trades` para ver histórico
4. Reinicie com `/resume` se necessário

---

**🎯 Lembre-se: Comece pequeno, monitore sempre, e aumente gradualmente conforme ganha experiência!**
