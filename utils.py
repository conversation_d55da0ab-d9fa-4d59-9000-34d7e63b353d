"""
Utility functions for VWAP, RSI calculations, and deposit monitoring
"""
import asyncio
import json
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Dict, Any
import numpy as np
import pandas as pd
from solana.rpc.async_api import AsyncClient
from solana.rpc.websocket_api import connect
from solders.pubkey import Pubkey
import structlog

from models import PriceData
from db import get_db_session
from sqlmodel import select

logger = structlog.get_logger(__name__)


class TechnicalIndicators:
    """Technical analysis indicators"""
    
    @staticmethod
    def calculate_vwap(prices: List[float], volumes: List[float], period: int = 60) -> float:
        """Calculate Volume Weighted Average Price"""
        if len(prices) < period or len(volumes) < period:
            return prices[-1] if prices else 0.0
        
        recent_prices = prices[-period:]
        recent_volumes = volumes[-period:]
        
        total_volume = sum(recent_volumes)
        if total_volume == 0:
            return recent_prices[-1]
        
        weighted_sum = sum(p * v for p, v in zip(recent_prices, recent_volumes))
        return weighted_sum / total_volume
    
    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> float:
        """Calculate Relative Strength Index"""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI
        
        # Calculate price changes
        changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        
        # Separate gains and losses
        gains = [change if change > 0 else 0 for change in changes]
        losses = [-change if change < 0 else 0 for change in changes]
        
        # Calculate average gains and losses
        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def calculate_sma(prices: List[float], period: int) -> float:
        """Calculate Simple Moving Average"""
        if len(prices) < period:
            return prices[-1] if prices else 0.0
        
        return sum(prices[-period:]) / period


class PriceDataManager:
    """Manages price data collection and storage"""
    
    def __init__(self, rpc_url: str):
        self.rpc_url = rpc_url
        self.price_cache: Dict[str, List[Dict]] = {}
    
    async def store_price_data(self, symbol: str, price: float, volume: float = None, 
                              vwap: float = None, rsi: float = None, source: str = "pyth"):
        """Store price data in database"""
        async for session in get_db_session():
            price_data = PriceData(
                symbol=symbol,
                price=Decimal(str(price)),
                volume=Decimal(str(volume)) if volume else None,
                vwap=Decimal(str(vwap)) if vwap else None,
                rsi=Decimal(str(rsi)) if rsi else None,
                source=source
            )
            
            session.add(price_data)
            await session.commit()
    
    async def get_recent_prices(self, symbol: str, hours: int = 24) -> List[PriceData]:
        """Get recent price data from database"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        async for session in get_db_session():
            result = await session.execute(
                select(PriceData)
                .where(PriceData.symbol == symbol)
                .where(PriceData.timestamp >= cutoff_time)
                .order_by(PriceData.timestamp)
            )
            return result.scalars().all()
    
    async def calculate_indicators(self, symbol: str, vwap_period: int = 60, 
                                 rsi_period: int = 14) -> Dict[str, float]:
        """Calculate technical indicators for symbol"""
        price_data = await self.get_recent_prices(symbol, hours=24)
        
        if not price_data:
            return {"price": 0.0, "vwap": 0.0, "rsi": 50.0}
        
        prices = [float(pd.price) for pd in price_data]
        volumes = [float(pd.volume) if pd.volume else 1.0 for pd in price_data]
        
        current_price = prices[-1] if prices else 0.0
        vwap = TechnicalIndicators.calculate_vwap(prices, volumes, vwap_period)
        rsi = TechnicalIndicators.calculate_rsi(prices, rsi_period)
        
        return {
            "price": current_price,
            "vwap": vwap,
            "rsi": rsi,
            "data_points": len(prices)
        }


class DepositMonitor:
    """Monitors wallet for incoming deposits"""
    
    def __init__(self, wallet_address: str, rpc_url: str, callback=None):
        self.wallet_address = wallet_address
        self.rpc_url = rpc_url
        self.callback = callback
        self.monitoring = False
        self.last_balance = 0.0
    
    async def start_monitoring(self):
        """Start monitoring for deposits"""
        self.monitoring = True
        logger.info("Starting deposit monitoring", address=self.wallet_address)
        
        while self.monitoring:
            try:
                await self._check_balance_change()
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error("Error in deposit monitoring", error=str(e))
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _check_balance_change(self):
        """Check for balance changes"""
        async with AsyncClient(self.rpc_url) as client:
            try:
                # Check SOL balance
                response = await client.get_balance(Pubkey.from_string(self.wallet_address))
                current_balance = response.value / 1e9 if response.value else 0.0
                
                if self.last_balance > 0 and current_balance > self.last_balance:
                    deposit_amount = current_balance - self.last_balance
                    logger.info("Deposit detected", 
                               amount=deposit_amount,
                               new_balance=current_balance)
                    
                    if self.callback:
                        await self.callback("SOL", deposit_amount, current_balance)
                
                self.last_balance = current_balance
                
            except Exception as e:
                logger.warning("Failed to check balance", error=str(e))
    
    def stop_monitoring(self):
        """Stop deposit monitoring"""
        self.monitoring = False
        logger.info("Stopped deposit monitoring")


class PythPriceClient:
    """Pyth Network price client for real-time SOL price"""
    
    def __init__(self, rpc_url: str, sol_price_feed: str):
        self.rpc_url = rpc_url
        self.sol_price_feed = sol_price_feed
    
    async def get_sol_price(self) -> Optional[float]:
        """Get current SOL price from Pyth"""
        try:
            async with AsyncClient(self.rpc_url) as client:
                # Get Pyth price account data
                response = await client.get_account_info(
                    Pubkey.from_string(self.sol_price_feed)
                )
                
                if response.value and response.value.data:
                    # Parse Pyth price data (simplified)
                    # In production, use proper Pyth client library
                    # This is a placeholder implementation
                    return 100.0  # Placeholder price
                
        except Exception as e:
            logger.error("Failed to get Pyth price", error=str(e))
        
        return None
    
    async def get_price_with_confidence(self) -> Dict[str, Any]:
        """Get price with confidence interval"""
        price = await self.get_sol_price()
        
        return {
            "price": price,
            "confidence": 0.01 if price else None,
            "timestamp": datetime.utcnow(),
            "source": "pyth"
        }


def format_currency(amount: float, currency: str = "USDC", decimals: int = 2) -> str:
    """Format currency amount for display"""
    if currency == "SOL":
        return f"{amount:.4f} SOL"
    elif currency == "USDC":
        return f"${amount:,.{decimals}f}"
    else:
        return f"{amount:.{decimals}f} {currency}"


def calculate_pnl_percentage(entry_price: float, current_price: float, side: str) -> float:
    """Calculate PnL percentage"""
    if entry_price == 0:
        return 0.0
    
    if side.upper() == "LONG":
        return ((current_price - entry_price) / entry_price) * 100
    else:  # SHORT
        return ((entry_price - current_price) / entry_price) * 100


def is_market_hours() -> bool:
    """Check if it's trading hours (24/5 - Monday to Friday)"""
    now = datetime.utcnow()
    weekday = now.weekday()  # 0 = Monday, 6 = Sunday
    
    # Monday 00:00 UTC to Friday 23:59 UTC
    return 0 <= weekday <= 4


def time_until_market_open() -> Optional[timedelta]:
    """Calculate time until market opens (Monday 00:00 UTC)"""
    now = datetime.utcnow()
    weekday = now.weekday()
    
    if 0 <= weekday <= 4:  # Already in trading hours
        return None
    
    # Calculate next Monday
    days_until_monday = (7 - weekday) % 7
    if days_until_monday == 0:  # It's Sunday
        days_until_monday = 1
    
    next_monday = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)
    return next_monday - now
