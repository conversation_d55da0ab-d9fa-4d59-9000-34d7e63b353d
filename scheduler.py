"""
24/5 trading cycle management with APScheduler
"""
import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Optional, Callable
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
import structlog

from db import get_setting, set_setting, get_db_session
from models import Balance, PriceData
from wallet import get_wallet_manager
from utils import is_market_hours, time_until_market_open, PriceDataManager, PythPriceClient
from strategy import get_strategy, signal_filter
from trader import get_traders
from risk import risk_manager, position_monitor

logger = structlog.get_logger(__name__)


class TradingScheduler:
    """Manages 24/5 trading schedule and execution"""
    
    def __init__(self, notification_callback: Optional[Callable] = None):
        self.scheduler = AsyncIOScheduler()
        self.notification_callback = notification_callback
        self.is_running = False
        self.price_manager: Optional[PriceDataManager] = None
        self.pyth_client: Optional[PythPriceClient] = None
        
    async def initialize(self, rpc_url: str, sol_price_feed: str):
        """Initialize scheduler components"""
        self.price_manager = PriceDataManager(rpc_url)
        self.pyth_client = PythPriceClient(rpc_url, sol_price_feed)
        
        # Initialize strategy with price manager
        from strategy import init_strategy
        init_strategy(self.price_manager)
        
        logger.info("Trading scheduler initialized")
    
    async def start(self):
        """Start the trading scheduler"""
        if self.is_running:
            logger.warning("Scheduler already running")
            return
        
        # Check if bot is active
        bot_active = await get_setting("BOT_ACTIVE", "true")
        if bot_active.lower() != "true":
            logger.info("Bot is paused, not starting scheduler")
            return
        
        # Get schedule interval
        interval_sec = int(await get_setting("SCHED_INTERVAL_SEC", "60"))
        
        # Add main trading job
        self.scheduler.add_job(
            self._trading_cycle,
            trigger=IntervalTrigger(seconds=interval_sec),
            id="trading_cycle",
            max_instances=1,
            coalesce=True
        )
        
        # Add balance update job (every 5 minutes)
        self.scheduler.add_job(
            self._update_balance_snapshot,
            trigger=IntervalTrigger(minutes=5),
            id="balance_update",
            max_instances=1,
            coalesce=True
        )
        
        # Add position monitoring job (every 30 seconds)
        self.scheduler.add_job(
            self._monitor_positions,
            trigger=IntervalTrigger(seconds=30),
            id="position_monitor",
            max_instances=1,
            coalesce=True
        )
        
        # Add daily reset job (at midnight UTC)
        self.scheduler.add_job(
            self._daily_reset,
            trigger="cron",
            hour=0,
            minute=0,
            id="daily_reset",
            max_instances=1
        )
        
        self.scheduler.start()
        self.is_running = True
        
        logger.info("Trading scheduler started", interval_sec=interval_sec)
        
        if self.notification_callback:
            await self.notification_callback("🚀 Trading bot started and monitoring markets 24/5")
    
    async def stop(self):
        """Stop the trading scheduler"""
        if not self.is_running:
            return
        
        self.scheduler.shutdown(wait=True)
        self.is_running = False
        
        logger.info("Trading scheduler stopped")
        
        if self.notification_callback:
            await self.notification_callback("⏹️ Trading bot stopped")
    
    async def pause(self):
        """Pause trading (keep monitoring but don't trade)"""
        await set_setting("BOT_ACTIVE", "false", "Bot paused by user")
        logger.info("Trading paused")
        
        if self.notification_callback:
            await self.notification_callback("⏸️ Trading paused - monitoring continues")
    
    async def resume(self):
        """Resume trading"""
        await set_setting("BOT_ACTIVE", "true", "Bot resumed by user")
        logger.info("Trading resumed")
        
        if self.notification_callback:
            await self.notification_callback("▶️ Trading resumed")
    
    async def _trading_cycle(self):
        """Main trading cycle - runs every minute"""
        try:
            # Check if bot is active
            bot_active = await get_setting("BOT_ACTIVE", "true")
            if bot_active.lower() != "true":
                return
            
            # Check market hours (24/5)
            if not is_market_hours():
                # Calculate time until market opens
                time_until_open = time_until_market_open()
                if time_until_open:
                    logger.info("Market closed", time_until_open=str(time_until_open))
                return
            
            # Check if daily target reached
            daily_target_reached = await get_setting("DAILY_TARGET_REACHED", "false")
            if daily_target_reached.lower() == "true":
                logger.info("Daily target reached, skipping trading")
                return
            
            # Get current SOL price
            current_price = await self._get_current_sol_price()
            if not current_price:
                logger.warning("Failed to get current SOL price")
                return
            
            # Store price data
            await self._store_price_data(current_price)
            
            # Check daily risk limits
            risk_status = await risk_manager.check_daily_risk_limits()
            if not risk_status["within_limits"]:
                logger.warning("Daily risk limits exceeded", risk_status=risk_status)
                return
            
            # Get trading signal
            strategy = get_strategy()
            signal_data = await strategy.get_trading_signal()
            
            # Add signal to filter
            signal_filter.add_signal(signal_data)
            
            # Validate signal
            if signal_data["signal"] != "NO_SIGNAL":
                if await strategy.validate_signal(signal_data):
                    if signal_filter.filter_duplicate_signals(signal_data):
                        await self._execute_trade(signal_data, current_price)
            
            # Check if daily target reached after potential trade
            if await risk_manager.check_daily_target_reached():
                await set_setting("DAILY_TARGET_REACHED", "true")
                if self.notification_callback:
                    await self.notification_callback(
                        "🎯 Daily target of +0.20% reached! Trading paused until tomorrow."
                    )
        
        except Exception as e:
            logger.error("Error in trading cycle", error=str(e))
    
    async def _get_current_sol_price(self) -> Optional[Decimal]:
        """Get current SOL price from Pyth"""
        try:
            if self.pyth_client:
                price_data = await self.pyth_client.get_price_with_confidence()
                if price_data["price"]:
                    return Decimal(str(price_data["price"]))
            
            # Fallback to a default price for testing
            return Decimal("100.0")
            
        except Exception as e:
            logger.error("Failed to get SOL price", error=str(e))
            return None
    
    async def _store_price_data(self, price: Decimal):
        """Store price data and calculate indicators"""
        try:
            if self.price_manager:
                # Calculate indicators
                indicators = await self.price_manager.calculate_indicators("SOL/USDC")
                
                # Store price with indicators
                await self.price_manager.store_price_data(
                    symbol="SOL/USDC",
                    price=float(price),
                    volume=1000.0,  # Placeholder volume
                    vwap=indicators.get("vwap"),
                    rsi=indicators.get("rsi"),
                    source="pyth"
                )
        except Exception as e:
            logger.error("Failed to store price data", error=str(e))
    
    async def _execute_trade(self, signal_data: dict, current_price: Decimal):
        """Execute trade based on signal"""
        try:
            # Get current balance
            wallet_mgr = get_wallet_manager()
            balances = await wallet_mgr.get_balances()
            
            # Calculate total balance in USDC
            total_usdc = Decimal(str(balances["USDC"])) + (Decimal(str(balances["SOL"])) * current_price)
            
            if total_usdc < Decimal("10"):  # Minimum $10 balance
                logger.warning("Insufficient balance for trading", balance=float(total_usdc))
                return
            
            # Execute trade
            jupiter_trader, leverage_trader = get_traders()
            
            result = await leverage_trader.open_position(
                side=signal_data["signal"],
                sol_price=current_price,
                balance_usdc=total_usdc
            )
            
            if result:
                # Send notification
                if self.notification_callback:
                    message = (
                        f"🔥 {result['side']} position opened!\n"
                        f"💰 Entry: ${result['entry_price']:.4f}\n"
                        f"📊 Notional: ${result['notional_usdc']:,.2f} (5x leverage)\n"
                        f"🎯 Target: ${result['target_price']:.4f}\n"
                        f"🛑 Stop: ${result['stop_price']:.4f}\n"
                        f"📈 Max Loss: ${result['max_loss_usdc']:,.2f}\n"
                        f"🔗 Signature: {result['signature'][:8]}..."
                    )
                    await self.notification_callback(message)
                
                logger.info("Trade executed successfully", result=result)
            
        except Exception as e:
            logger.error("Failed to execute trade", error=str(e))
    
    async def _monitor_positions(self):
        """Monitor open positions for target/stop conditions"""
        try:
            current_price = await self._get_current_sol_price()
            if not current_price:
                return
            
            # Update position PnL
            jupiter_trader, leverage_trader = get_traders()
            await leverage_trader.update_position_pnl(current_price)
            
            # Check for position exits
            actions = await position_monitor.check_open_positions(current_price)
            
            for action in actions:
                await self._handle_position_exit(action, current_price)
                
        except Exception as e:
            logger.error("Error monitoring positions", error=str(e))
    
    async def _handle_position_exit(self, action: dict, current_price: Decimal):
        """Handle position exit (target or stop loss)"""
        try:
            jupiter_trader, leverage_trader = get_traders()
            
            result = await leverage_trader.close_position(
                trade_id=action["trade_id"],
                current_price=current_price,
                reason=action["reason"]
            )
            
            if result and self.notification_callback:
                pnl_emoji = "💚" if result["realized_pnl"] > 0 else "❌"
                message = (
                    f"{pnl_emoji} Position closed: {action['reason']}\n"
                    f"💰 Exit: ${result['exit_price']:.4f}\n"
                    f"📊 PnL: ${result['realized_pnl']:,.2f} ({result['pnl_percentage']:+.2f}%)\n"
                    f"🔗 Signature: {result['signature'][:8]}..."
                )
                await self.notification_callback(message)
                
        except Exception as e:
            logger.error("Failed to handle position exit", error=str(e))
    
    async def _update_balance_snapshot(self):
        """Update balance snapshot in database"""
        try:
            wallet_mgr = get_wallet_manager()
            balances = await wallet_mgr.get_balances()
            current_price = await self._get_current_sol_price()
            
            if current_price:
                total_value_usdc = Decimal(str(balances["USDC"])) + (Decimal(str(balances["SOL"])) * current_price)
                
                # Calculate daily PnL
                daily_pnl = await self._calculate_daily_pnl()
                
                # Save balance snapshot
                async for session in get_db_session():
                    balance = Balance(
                        sol_balance=Decimal(str(balances["SOL"])),
                        usdc_balance=Decimal(str(balances["USDC"])),
                        total_value_usdc=total_value_usdc,
                        daily_pnl=daily_pnl
                    )
                    session.add(balance)
                    await session.commit()
                    
        except Exception as e:
            logger.error("Failed to update balance snapshot", error=str(e))
    
    async def _calculate_daily_pnl(self) -> Decimal:
        """Calculate daily PnL from trades"""
        try:
            from datetime import timezone
            today = datetime.now(timezone.utc).date()

            async for session in get_db_session():
                from sqlmodel import select, func
                from models import Trade
                
                result = await session.execute(
                    select(func.sum(Trade.realized_pnl))
                    .where(func.date(Trade.entry_time) == today)
                    .where(Trade.realized_pnl.isnot(None))
                )
                
                daily_pnl = result.scalar()
                return daily_pnl if daily_pnl else Decimal("0")
                
        except Exception as e:
            logger.error("Failed to calculate daily PnL", error=str(e))
            return Decimal("0")
    
    async def _daily_reset(self):
        """Reset daily flags at midnight UTC"""
        try:
            await set_setting("DAILY_TARGET_REACHED", "false")
            await set_setting("LAST_RESET_DATE", datetime.now(datetime.UTC).date().isoformat())
            
            logger.info("Daily reset completed")
            
            if self.notification_callback:
                await self.notification_callback("🌅 New trading day started! Daily limits reset.")
                
        except Exception as e:
            logger.error("Failed to perform daily reset", error=str(e))


# Global scheduler instance
trading_scheduler: Optional[TradingScheduler] = None


def init_scheduler(notification_callback: Optional[Callable] = None) -> TradingScheduler:
    """Initialize global scheduler instance"""
    global trading_scheduler
    trading_scheduler = TradingScheduler(notification_callback)
    return trading_scheduler


def get_scheduler() -> TradingScheduler:
    """Get global scheduler instance"""
    if trading_scheduler is None:
        raise RuntimeError("Scheduler not initialized")
    return trading_scheduler
