"""
Unit tests for risk management module
"""
import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from risk import RiskManager, PositionMonitor
from models import Trade, Balance


class TestRiskManager:
    """Test risk management calculations"""
    
    @pytest.fixture
    def risk_manager(self):
        """Create risk manager instance"""
        return RiskManager()
    
    def test_calculate_position_size_basic(self, risk_manager):
        """Test basic position size calculation"""
        balance_usdc = Decimal("1000.0")
        sol_price = Decimal("100.0")
        
        result = risk_manager.calculate_position_size(balance_usdc, sol_price)
        
        # Expected: 1000 * 0.90 * 5 = 4500 USDC notional
        assert result["notional_usdc"] == Decimal("4500.00")
        
        # Expected: 4500 / 100 = 45 SOL
        assert result["sol_amount"] == Decimal("45.0000")
        
        # Expected: 4500 * 0.0015 = 6.75 USDC max loss
        assert result["max_loss_usdc"] == Decimal("6.75")
        
        # Expected: 6.75 / 1000 = 0.675% risk
        assert result["risk_percentage"] == Decimal("0.0068")  # 0.675% rounded
        
        # Expected: 5x leverage
        assert result["leverage_used"] == Decimal("5")
    
    def test_calculate_position_size_small_balance(self, risk_manager):
        """Test position size with small balance"""
        balance_usdc = Decimal("50.0")
        sol_price = Decimal("100.0")
        
        result = risk_manager.calculate_position_size(balance_usdc, sol_price)
        
        # Expected: 50 * 0.90 * 5 = 225 USDC notional
        assert result["notional_usdc"] == Decimal("225.00")
        assert result["sol_amount"] == Decimal("2.2500")
        assert result["max_loss_usdc"] == Decimal("0.34")  # 225 * 0.0015
    
    def test_calculate_target_stop_prices_long(self, risk_manager):
        """Test target and stop price calculation for LONG"""
        entry_price = Decimal("100.0")
        side = "LONG"
        
        target_price, stop_price = risk_manager.calculate_target_stop_prices(entry_price, side)
        
        # Expected target: 100 * (1 + 0.002) = 100.20
        assert target_price == Decimal("100.2000")
        
        # Expected stop: 100 * (1 - 0.0015) = 99.85
        assert stop_price == Decimal("99.8500")
    
    def test_calculate_target_stop_prices_short(self, risk_manager):
        """Test target and stop price calculation for SHORT"""
        entry_price = Decimal("100.0")
        side = "SHORT"
        
        target_price, stop_price = risk_manager.calculate_target_stop_prices(entry_price, side)
        
        # Expected target: 100 * (1 - 0.002) = 99.80
        assert target_price == Decimal("99.8000")
        
        # Expected stop: 100 * (1 + 0.0015) = 100.15
        assert stop_price == Decimal("100.1500")
    
    def test_validate_trade_parameters_valid(self, risk_manager):
        """Test trade validation with valid parameters"""
        balance_usdc = Decimal("1000.0")
        sol_price = Decimal("100.0")
        
        result = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
        
        assert result["valid"] is True
        assert "position_info" in result
    
    def test_validate_trade_parameters_insufficient_balance(self, risk_manager):
        """Test trade validation with insufficient balance"""
        balance_usdc = Decimal("0.0")
        sol_price = Decimal("100.0")
        
        result = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
        
        assert result["valid"] is False
        assert "Insufficient balance" in result["reason"]
    
    def test_validate_trade_parameters_invalid_price(self, risk_manager):
        """Test trade validation with invalid price"""
        balance_usdc = Decimal("1000.0")
        sol_price = Decimal("0.0")
        
        result = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
        
        assert result["valid"] is False
        assert "Invalid SOL price" in result["reason"]
    
    def test_validate_trade_parameters_position_too_small(self, risk_manager):
        """Test trade validation with position too small"""
        balance_usdc = Decimal("5.0")  # Very small balance
        sol_price = Decimal("100.0")
        
        result = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
        
        # Position would be 5 * 0.90 * 5 = 22.5 USDC, which is > 10 minimum
        # So this should actually be valid
        assert result["valid"] is True
    
    def test_validate_trade_parameters_excessive_risk(self, risk_manager):
        """Test trade validation with excessive risk"""
        # Set very low risk tolerance
        risk_manager.max_daily_risk = Decimal("0.001")  # 0.1%
        
        balance_usdc = Decimal("1000.0")
        sol_price = Decimal("100.0")
        
        result = risk_manager.validate_trade_parameters(balance_usdc, sol_price)
        
        # Risk would be 0.675%, which exceeds 0.1% limit
        assert result["valid"] is False
        assert "exceeds daily limit" in result["reason"]
    
    @pytest.mark.asyncio
    async def test_update_from_settings(self, risk_manager):
        """Test updating risk parameters from settings"""
        with patch('risk.get_setting') as mock_get_setting:
            mock_get_setting.side_effect = lambda key, default: {
                "TRADE_PCT": "0.80",
                "LEVERAGE": "3",
                "TARGET_PCT": "0.003",
                "STOP_PCT": "0.002",
                "MAX_DD_PCT": "0.02"
            }.get(key, default)
            
            await risk_manager.update_from_settings()
            
            assert risk_manager.trade_pct == Decimal("0.80")
            assert risk_manager.leverage == Decimal("3")
            assert risk_manager.target_pct == Decimal("0.003")
            assert risk_manager.stop_pct == Decimal("0.002")
            assert risk_manager.max_daily_risk == Decimal("0.02")


class TestPositionMonitor:
    """Test position monitoring logic"""
    
    @pytest.fixture
    def risk_manager(self):
        """Create risk manager instance"""
        return RiskManager()
    
    @pytest.fixture
    def position_monitor(self, risk_manager):
        """Create position monitor instance"""
        return PositionMonitor(risk_manager)
    
    def test_check_position_exit_long_target_hit(self, position_monitor):
        """Test LONG position target hit"""
        trade = Trade(
            id=1,
            side="LONG",
            entry_price=Decimal("100.0"),
            target_price=Decimal("100.20"),
            stop_price=Decimal("99.85"),
            status="OPEN"
        )
        
        current_price = Decimal("100.25")  # Above target
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is not None
        assert action["action"] == "CLOSE_TARGET"
        assert action["trade_id"] == 1
        assert action["reason"] == "Target reached"
    
    def test_check_position_exit_long_stop_hit(self, position_monitor):
        """Test LONG position stop loss hit"""
        trade = Trade(
            id=1,
            side="LONG",
            entry_price=Decimal("100.0"),
            target_price=Decimal("100.20"),
            stop_price=Decimal("99.85"),
            status="OPEN"
        )
        
        current_price = Decimal("99.80")  # Below stop
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is not None
        assert action["action"] == "CLOSE_STOP"
        assert action["trade_id"] == 1
        assert action["reason"] == "Stop loss triggered"
    
    def test_check_position_exit_short_target_hit(self, position_monitor):
        """Test SHORT position target hit"""
        trade = Trade(
            id=1,
            side="SHORT",
            entry_price=Decimal("100.0"),
            target_price=Decimal("99.80"),
            stop_price=Decimal("100.15"),
            status="OPEN"
        )
        
        current_price = Decimal("99.75")  # Below target
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is not None
        assert action["action"] == "CLOSE_TARGET"
        assert action["trade_id"] == 1
        assert action["reason"] == "Target reached"
    
    def test_check_position_exit_short_stop_hit(self, position_monitor):
        """Test SHORT position stop loss hit"""
        trade = Trade(
            id=1,
            side="SHORT",
            entry_price=Decimal("100.0"),
            target_price=Decimal("99.80"),
            stop_price=Decimal("100.15"),
            status="OPEN"
        )
        
        current_price = Decimal("100.20")  # Above stop
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is not None
        assert action["action"] == "CLOSE_STOP"
        assert action["trade_id"] == 1
        assert action["reason"] == "Stop loss triggered"
    
    def test_check_position_exit_no_action(self, position_monitor):
        """Test position with no exit action needed"""
        trade = Trade(
            id=1,
            side="LONG",
            entry_price=Decimal("100.0"),
            target_price=Decimal("100.20"),
            stop_price=Decimal("99.85"),
            status="OPEN"
        )
        
        current_price = Decimal("100.10")  # Between stop and target
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is None
    
    def test_check_position_exit_missing_prices(self, position_monitor):
        """Test position with missing target/stop prices"""
        trade = Trade(
            id=1,
            side="LONG",
            entry_price=Decimal("100.0"),
            target_price=None,
            stop_price=None,
            status="OPEN"
        )
        
        current_price = Decimal("100.10")
        
        action = position_monitor._check_position_exit(trade, current_price)
        
        assert action is None


class TestRiskCalculations:
    """Test various risk calculation scenarios"""
    
    def test_leverage_amplification(self):
        """Test that leverage properly amplifies position size"""
        risk_manager = RiskManager()
        
        balance = Decimal("1000.0")
        price = Decimal("100.0")
        
        # Test with different leverage values
        risk_manager.leverage = Decimal("1")
        result_1x = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.leverage = Decimal("5")
        result_5x = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.leverage = Decimal("10")
        result_10x = risk_manager.calculate_position_size(balance, price)
        
        # Notional should scale with leverage
        assert result_5x["notional_usdc"] == result_1x["notional_usdc"] * 5
        assert result_10x["notional_usdc"] == result_1x["notional_usdc"] * 10
        
        # Risk should also scale with leverage
        assert result_5x["max_loss_usdc"] == result_1x["max_loss_usdc"] * 5
        assert result_10x["max_loss_usdc"] == result_1x["max_loss_usdc"] * 10
    
    def test_trade_percentage_impact(self):
        """Test impact of trade percentage on position size"""
        risk_manager = RiskManager()
        
        balance = Decimal("1000.0")
        price = Decimal("100.0")
        
        # Test with different trade percentages
        risk_manager.trade_pct = Decimal("0.50")  # 50%
        result_50 = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.trade_pct = Decimal("0.90")  # 90%
        result_90 = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.trade_pct = Decimal("1.00")  # 100%
        result_100 = risk_manager.calculate_position_size(balance, price)
        
        # Notional should scale with trade percentage
        expected_ratio_90_50 = Decimal("0.90") / Decimal("0.50")
        actual_ratio_90_50 = result_90["notional_usdc"] / result_50["notional_usdc"]
        assert abs(actual_ratio_90_50 - expected_ratio_90_50) < Decimal("0.01")
        
        expected_ratio_100_90 = Decimal("1.00") / Decimal("0.90")
        actual_ratio_100_90 = result_100["notional_usdc"] / result_90["notional_usdc"]
        assert abs(actual_ratio_100_90 - expected_ratio_100_90) < Decimal("0.01")
    
    def test_stop_loss_percentage_impact(self):
        """Test impact of stop loss percentage on max loss"""
        risk_manager = RiskManager()
        
        balance = Decimal("1000.0")
        price = Decimal("100.0")
        
        # Test with different stop loss percentages
        risk_manager.stop_pct = Decimal("0.001")  # 0.1%
        result_01 = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.stop_pct = Decimal("0.0015")  # 0.15%
        result_015 = risk_manager.calculate_position_size(balance, price)
        
        risk_manager.stop_pct = Decimal("0.002")  # 0.2%
        result_02 = risk_manager.calculate_position_size(balance, price)
        
        # Max loss should scale with stop percentage
        expected_ratio_015_01 = Decimal("0.0015") / Decimal("0.001")
        actual_ratio_015_01 = result_015["max_loss_usdc"] / result_01["max_loss_usdc"]
        assert abs(actual_ratio_015_01 - expected_ratio_015_01) < Decimal("0.01")
        
        expected_ratio_02_015 = Decimal("0.002") / Decimal("0.0015")
        actual_ratio_02_015 = result_02["max_loss_usdc"] / result_015["max_loss_usdc"]
        assert abs(actual_ratio_02_015 - expected_ratio_02_015) < Decimal("0.01")


if __name__ == "__main__":
    pytest.main([__file__])
