"""
Unit tests for trading strategy module
"""
import pytest
import asyncio
from decimal import Decimal
from unittest.mock import AsyncMock, MagicMock, patch

from strategy import TradingStrategy, SignalFilter
from utils import PriceDataManager, TechnicalIndicators


class TestTechnicalIndicators:
    """Test technical indicator calculations"""
    
    def test_calculate_vwap(self):
        """Test VWAP calculation"""
        prices = [100.0, 101.0, 102.0, 103.0, 104.0]
        volumes = [1000.0, 1500.0, 1200.0, 800.0, 900.0]
        
        vwap = TechnicalIndicators.calculate_vwap(prices, volumes, period=5)
        
        # Calculate expected VWAP manually
        total_volume = sum(volumes)
        weighted_sum = sum(p * v for p, v in zip(prices, volumes))
        expected_vwap = weighted_sum / total_volume
        
        assert abs(vwap - expected_vwap) < 0.001
    
    def test_calculate_vwap_insufficient_data(self):
        """Test VWAP with insufficient data"""
        prices = [100.0, 101.0]
        volumes = [1000.0, 1500.0]
        
        vwap = TechnicalIndicators.calculate_vwap(prices, volumes, period=5)
        
        # Should return last price when insufficient data
        assert vwap == 101.0
    
    def test_calculate_rsi(self):
        """Test RSI calculation"""
        # Create price series with known RSI
        prices = [
            44.0, 44.25, 44.5, 43.75, 44.5, 44.75, 44.5, 44.25,
            44.0, 44.25, 45.0, 45.25, 45.5, 45.75, 46.0, 46.25
        ]
        
        rsi = TechnicalIndicators.calculate_rsi(prices, period=14)
        
        # RSI should be between 0 and 100
        assert 0 <= rsi <= 100
        
        # For this upward trending series, RSI should be > 50
        assert rsi > 50
    
    def test_calculate_rsi_insufficient_data(self):
        """Test RSI with insufficient data"""
        prices = [100.0, 101.0, 102.0]
        
        rsi = TechnicalIndicators.calculate_rsi(prices, period=14)
        
        # Should return neutral RSI (50) when insufficient data
        assert rsi == 50.0
    
    def test_calculate_sma(self):
        """Test Simple Moving Average calculation"""
        prices = [100.0, 102.0, 104.0, 106.0, 108.0]
        
        sma = TechnicalIndicators.calculate_sma(prices, period=5)
        
        expected_sma = sum(prices) / len(prices)
        assert abs(sma - expected_sma) < 0.001


class TestTradingStrategy:
    """Test trading strategy logic"""
    
    @pytest.fixture
    def mock_price_manager(self):
        """Create mock price manager"""
        manager = MagicMock(spec=PriceDataManager)
        return manager
    
    @pytest.fixture
    def strategy(self, mock_price_manager):
        """Create strategy instance"""
        return TradingStrategy(mock_price_manager)
    
    @pytest.mark.asyncio
    async def test_get_trading_signal_long(self, strategy, mock_price_manager):
        """Test LONG signal generation"""
        # Mock indicators that should generate LONG signal
        mock_price_manager.calculate_indicators.return_value = {
            "price": 99.0,
            "vwap": 100.0,  # price < vwap * 0.9975 = 99.75
            "rsi": 25.0,    # rsi < 30
            "data_points": 150
        }
        
        with patch('strategy.is_market_hours', return_value=True):
            signal = await strategy.get_trading_signal()
        
        assert signal["signal"] == "LONG"
        assert signal["confidence"] > 0
        assert "price" in signal["reason"]
        assert "RSI" in signal["reason"]
    
    @pytest.mark.asyncio
    async def test_get_trading_signal_short(self, strategy, mock_price_manager):
        """Test SHORT signal generation"""
        # Mock indicators that should generate SHORT signal
        mock_price_manager.calculate_indicators.return_value = {
            "price": 101.0,
            "vwap": 100.0,  # price > vwap * 1.0025 = 100.25
            "rsi": 75.0,    # rsi > 70
            "data_points": 150
        }
        
        with patch('strategy.is_market_hours', return_value=True):
            signal = await strategy.get_trading_signal()
        
        assert signal["signal"] == "SHORT"
        assert signal["confidence"] > 0
    
    @pytest.mark.asyncio
    async def test_get_trading_signal_no_signal(self, strategy, mock_price_manager):
        """Test NO_SIGNAL when conditions not met"""
        # Mock indicators that should NOT generate signal
        mock_price_manager.calculate_indicators.return_value = {
            "price": 100.0,
            "vwap": 100.0,  # price/vwap = 1.0 (neutral)
            "rsi": 50.0,    # rsi = 50 (neutral)
            "data_points": 150
        }
        
        with patch('strategy.is_market_hours', return_value=True):
            signal = await strategy.get_trading_signal()
        
        assert signal["signal"] == "NO_SIGNAL"
        assert signal["confidence"] == 0.0
    
    @pytest.mark.asyncio
    async def test_get_trading_signal_market_closed(self, strategy, mock_price_manager):
        """Test NO_SIGNAL when market is closed"""
        with patch('strategy.is_market_hours', return_value=False):
            signal = await strategy.get_trading_signal()
        
        assert signal["signal"] == "NO_SIGNAL"
        assert "Market closed" in signal["reason"]
    
    @pytest.mark.asyncio
    async def test_get_trading_signal_insufficient_data(self, strategy, mock_price_manager):
        """Test NO_SIGNAL when insufficient data"""
        mock_price_manager.calculate_indicators.return_value = {
            "price": 100.0,
            "vwap": 100.0,
            "rsi": 50.0,
            "data_points": 50  # Less than min_data_points (100)
        }
        
        with patch('strategy.is_market_hours', return_value=True):
            signal = await strategy.get_trading_signal()
        
        assert signal["signal"] == "NO_SIGNAL"
        assert "Insufficient data points" in signal["reason"]
    
    def test_analyze_signals_long_conditions(self, strategy):
        """Test signal analysis for LONG conditions"""
        price = 99.0
        vwap = 100.0
        rsi = 25.0
        
        result = strategy._analyze_signals(price, vwap, rsi)
        
        assert result["signal"] == "LONG"
        assert result["confidence"] > 0
        assert result["price_vwap_ratio"] == 0.99
    
    def test_analyze_signals_short_conditions(self, strategy):
        """Test signal analysis for SHORT conditions"""
        price = 101.0
        vwap = 100.0
        rsi = 75.0
        
        result = strategy._analyze_signals(price, vwap, rsi)
        
        assert result["signal"] == "SHORT"
        assert result["confidence"] > 0
        assert result["price_vwap_ratio"] == 1.01
    
    def test_calculate_confidence_long(self, strategy):
        """Test confidence calculation for LONG signals"""
        # Very extreme conditions should give high confidence
        confidence = strategy._calculate_confidence(0.990, 20.0, "LONG")
        assert confidence > 0.5
        
        # Less extreme conditions should give lower confidence
        confidence = strategy._calculate_confidence(0.997, 29.0, "LONG")
        assert 0 < confidence < 0.5
    
    def test_calculate_confidence_short(self, strategy):
        """Test confidence calculation for SHORT signals"""
        # Very extreme conditions should give high confidence
        confidence = strategy._calculate_confidence(1.010, 80.0, "SHORT")
        assert confidence > 0.5
        
        # Less extreme conditions should give lower confidence
        confidence = strategy._calculate_confidence(1.003, 71.0, "SHORT")
        assert 0 < confidence < 0.5
    
    @pytest.mark.asyncio
    async def test_validate_signal_low_confidence(self, strategy):
        """Test signal validation with low confidence"""
        signal_data = {
            "signal": "LONG",
            "confidence": 0.1  # Below minimum threshold (0.3)
        }
        
        with patch.object(strategy, '_has_recent_signal', return_value=False):
            is_valid = await strategy.validate_signal(signal_data)
        
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_signal_recent_signal(self, strategy):
        """Test signal validation with recent similar signal"""
        signal_data = {
            "signal": "LONG",
            "confidence": 0.8
        }
        
        with patch.object(strategy, '_has_recent_signal', return_value=True):
            is_valid = await strategy.validate_signal(signal_data)
        
        assert not is_valid
    
    @pytest.mark.asyncio
    async def test_validate_signal_valid(self, strategy):
        """Test signal validation with valid signal"""
        signal_data = {
            "signal": "LONG",
            "confidence": 0.8
        }
        
        with patch.object(strategy, '_has_recent_signal', return_value=False):
            is_valid = await strategy.validate_signal(signal_data)
        
        assert is_valid


class TestSignalFilter:
    """Test signal filtering logic"""
    
    @pytest.fixture
    def signal_filter(self):
        """Create signal filter instance"""
        return SignalFilter()
    
    def test_add_signal(self, signal_filter):
        """Test adding signals to history"""
        signal_data = {"signal": "LONG", "confidence": 0.8}
        
        signal_filter.add_signal(signal_data)
        
        assert len(signal_filter.signal_history) == 1
        assert "timestamp" in signal_filter.signal_history[0]
    
    def test_filter_duplicate_signals_no_duplicates(self, signal_filter):
        """Test filtering when no duplicates exist"""
        new_signal = {"signal": "LONG", "confidence": 0.8}
        
        result = signal_filter.filter_duplicate_signals(new_signal)
        
        assert result is True
    
    def test_filter_duplicate_signals_with_duplicates(self, signal_filter):
        """Test filtering when duplicates exist"""
        # Add a recent LONG signal
        signal_filter.add_signal({"signal": "LONG", "confidence": 0.8})
        
        # Try to add another LONG signal
        new_signal = {"signal": "LONG", "confidence": 0.7}
        result = signal_filter.filter_duplicate_signals(new_signal, minutes=15)
        
        assert result is False
    
    def test_filter_duplicate_signals_different_types(self, signal_filter):
        """Test filtering with different signal types"""
        # Add a LONG signal
        signal_filter.add_signal({"signal": "LONG", "confidence": 0.8})
        
        # Try to add a SHORT signal (should be allowed)
        new_signal = {"signal": "SHORT", "confidence": 0.7}
        result = signal_filter.filter_duplicate_signals(new_signal)
        
        assert result is True
    
    def test_filter_no_signal_always_allowed(self, signal_filter):
        """Test that NO_SIGNAL is always allowed"""
        new_signal = {"signal": "NO_SIGNAL", "confidence": 0.0}
        
        result = signal_filter.filter_duplicate_signals(new_signal)
        
        assert result is True


if __name__ == "__main__":
    pytest.main([__file__])
