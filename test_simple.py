#!/usr/bin/env python3
"""
Teste simples do SOL Trading Bot
"""
import os
import sys
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

def test_basic_imports():
    """Testar importações básicas"""
    print("🧪 Testando importações básicas...")
    
    try:
        from decimal import Decimal
        print("✅ decimal - OK")
    except Exception as e:
        print(f"❌ decimal - Erro: {e}")
    
    try:
        import structlog
        print("✅ structlog - OK")
    except Exception as e:
        print(f"❌ structlog - Erro: {e}")
    
    try:
        import sqlmodel
        print("✅ sqlmodel - OK")
    except Exception as e:
        print(f"❌ sqlmodel - Erro: {e}")
    
    try:
        import aiosqlite
        print("✅ aiosqlite - OK")
    except Exception as e:
        print(f"❌ aiosqlite - Erro: {e}")
    
    try:
        import nacl
        print("✅ PyNaCl - OK")
    except Exception as e:
        print(f"❌ PyNaCl - Erro: {e}")

def test_configuration():
    """Testar configuração"""
    print("\n⚙️ Testando configuração...")
    
    # Verificar variáveis de ambiente essenciais
    required_vars = ["TELEGRAM_TOKEN", "TELEGRAM_ADMIN_ID", "SOLANA_RPC"]
    
    all_configured = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mascarar valores sensíveis
            if "TOKEN" in var:
                display_value = f"{'*' * (len(value) - 4)}{value[-4:]}"
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: Não configurado")
            all_configured = False
    
    return all_configured

def test_python_version():
    """Testar versão do Python"""
    print("\n🐍 Testando versão do Python...")
    
    version = sys.version_info
    print(f"Python {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 11):
        print("✅ Versão do Python adequada")
        return True
    else:
        print("⚠️ Python 3.11+ recomendado")
        return False

def test_directories():
    """Testar criação de diretórios"""
    print("\n📁 Testando diretórios...")
    
    try:
        os.makedirs("./data", exist_ok=True)
        print("✅ Diretório ./data criado/verificado")
        return True
    except Exception as e:
        print(f"❌ Erro criando diretório: {e}")
        return False

def main():
    """Função principal"""
    print("🤖 SOL Trading Bot - Teste Simples\n")
    
    # Executar testes básicos
    test_basic_imports()
    python_ok = test_python_version()
    dirs_ok = test_directories()
    config_ok = test_configuration()
    
    print("\n📋 Resumo dos Testes:")
    print(f"✅ Python: {'OK' if python_ok else 'Atenção'}")
    print(f"✅ Diretórios: {'OK' if dirs_ok else 'Erro'}")
    print(f"✅ Configuração: {'OK' if config_ok else 'Incompleta'}")
    
    if config_ok and dirs_ok:
        print("\n🎉 Testes básicos passaram!")
        print("\n📋 Próximos passos:")
        print("1. Instalar dependências: pip install -r requirements.txt")
        print("2. Validar configuração: python validate_config.py")
        print("3. Executar bot: python -m bot")
        print("\n💡 O bot mostrará instruções completas no /start")
    else:
        print("\n⚠️ Alguns testes falharam. Verifique a configuração.")
        if not config_ok:
            print("   - Configure o arquivo .env com seus tokens do Telegram")
        if not dirs_ok:
            print("   - Verifique permissões de escrita no diretório")

if __name__ == "__main__":
    main()
