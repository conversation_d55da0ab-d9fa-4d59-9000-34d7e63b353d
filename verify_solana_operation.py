#!/usr/bin/env python3
"""
Verificação completa da operação do bot na rede Solana
"""
import asyncio
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

async def verify_bot_solana_operation():
    """Verificar se o bot está operando corretamente na Solana"""
    print("🔍 VERIFICAÇÃO COMPLETA - BOT SOLANA OPERATION\n")
    
    # 1. Verificar configurações
    print("1️⃣ Verificando configurações...")
    
    rpc_url = os.getenv("SOLANA_RPC")
    sol_price_feed = os.getenv("SOL_PRICE_FEED")
    
    print(f"   ✅ RPC URL: {rpc_url}")
    print(f"   ✅ Price Feed: {sol_price_feed}")
    print(f"   ✅ Rede: Solana Mainnet")
    
    # 2. Testar conexão RPC
    print("\n2️⃣ Testando conexão RPC...")
    
    try:
        import aiohttp
        
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getHealth"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(rpc_url, json=payload, timeout=10) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"   ✅ Solana RPC: {result.get('result', 'OK')}")
                else:
                    print(f"   ❌ RPC Error: {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ RPC Connection Error: {e}")
        return False
    
    # 3. Testar Jupiter API
    print("\n3️⃣ Testando Jupiter API...")
    
    try:
        jupiter_url = "https://quote-api.jup.ag/v6"
        
        # Teste de quote SOL -> USDC
        params = {
            "inputMint": "So11111111111111111111111111111111111111112",  # SOL
            "outputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "amount": "100000000",  # 0.1 SOL
            "slippageBps": "50"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{jupiter_url}/quote", params=params, timeout=10) as response:
                if response.status == 200:
                    result = await response.json()
                    input_amount = int(result.get("inAmount", 0)) / 1_000_000_000
                    output_amount = int(result.get("outAmount", 0)) / 1_000_000
                    price = output_amount / input_amount if input_amount > 0 else 0
                    print(f"   ✅ Jupiter API: {input_amount} SOL → {output_amount:.2f} USDC")
                    print(f"   📊 Preço SOL: ${price:.2f}")
                else:
                    print(f"   ❌ Jupiter Error: {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ Jupiter API Error: {e}")
        return False
    
    # 4. Verificar carteira
    print("\n4️⃣ Verificando carteira...")
    
    try:
        # Verificar arquivo TXT
        txt_path = "./data/keystore_backup.txt"
        if os.path.exists(txt_path):
            with open(txt_path, 'r') as f:
                content = f.read()
                
            # Extrair endereço
            for line in content.split('\n'):
                if line.startswith('Endereço da Carteira:'):
                    address = line.split(': ')[1].strip()
                    print(f"   ✅ Carteira TXT: {address}")
                    break
            
            # Verificar se a conta existe na rede
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": [address, {"encoding": "base64"}]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(rpc_url, json=payload, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        account_info = result.get("result", {}).get("value")
                        if account_info:
                            lamports = account_info.get("lamports", 0)
                            sol_balance = lamports / 1_000_000_000
                            print(f"   ✅ Saldo SOL: {sol_balance:.6f}")
                        else:
                            print(f"   ✅ Conta válida na rede (saldo zero)")
                    else:
                        print(f"   ❌ Erro verificando conta: {response.status}")
                        return False
        else:
            print(f"   ❌ Arquivo TXT não encontrado")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro na carteira: {e}")
        return False
    
    # 5. Verificar tokens suportados
    print("\n5️⃣ Verificando tokens...")
    
    sol_mint = "So11111111111111111111111111111111111111112"
    usdc_mint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    
    print(f"   ✅ SOL Mint: {sol_mint}")
    print(f"   ✅ USDC Mint: {usdc_mint}")
    
    # 6. Verificar feed de preços
    print("\n6️⃣ Verificando feed de preços...")
    
    try:
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getAccountInfo",
            "params": [sol_price_feed, {"encoding": "base64"}]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(rpc_url, json=payload, timeout=10) as response:
                if response.status == 200:
                    result = await response.json()
                    account_info = result.get("result", {}).get("value")
                    if account_info:
                        print(f"   ✅ Pyth Price Feed ativo")
                    else:
                        print(f"   ❌ Price Feed não encontrado")
                        return False
                else:
                    print(f"   ❌ Erro verificando feed: {response.status}")
                    return False
    except Exception as e:
        print(f"   ❌ Erro no price feed: {e}")
        return False
    
    # 7. Verificar status do bot
    print("\n7️⃣ Verificando status do bot...")
    
    try:
        # Verificar se o processo está rodando
        import subprocess
        result = subprocess.run(['pgrep', '-f', 'python.*bot'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Bot está rodando (PID: {result.stdout.strip()})")
        else:
            print(f"   ⚠️ Bot pode não estar rodando")
        
        # Verificar banco de dados
        db_path = "./data/solbot.db"
        if os.path.exists(db_path):
            print(f"   ✅ Banco de dados existe")
        else:
            print(f"   ⚠️ Banco de dados não encontrado")
            
    except Exception as e:
        print(f"   ⚠️ Erro verificando status: {e}")
    
    print("\n" + "="*50)
    print("🎉 VERIFICAÇÃO CONCLUÍDA")
    print("="*50)
    print("✅ Bot está configurado para operar na Solana Mainnet")
    print("✅ Conexões RPC e Jupiter funcionando")
    print("✅ Carteira válida e carregada do TXT")
    print("✅ Tokens SOL/USDC suportados")
    print("✅ Feed de preços Pyth ativo")
    print("\n📋 RESUMO DA OPERAÇÃO:")
    print("   • Rede: Solana Mainnet")
    print("   • Trading: SOL/USDC via Jupiter v6")
    print("   • Alavancagem: 5x simulada")
    print("   • Preços: Feed Pyth em tempo real")
    print("   • Carteira: Auto-custódia sem senha")
    print("   • Backup: TXT com palavra-passe drfs2015")
    
    return True

if __name__ == "__main__":
    asyncio.run(verify_bot_solana_operation())
