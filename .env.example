# Telegram Configuration
TELEGRAM_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz-EXAMPLE
TELEGRAM_ADMIN_ID=123456789

# Solana Network
SOLANA_RPC=https://api.mainnet-beta.solana.com
JUPITER_CLUSTER=mainnet-beta

# Trading Parameters
SLIPPAGE_BPS=15        # 0.15% slippage tolerance
TRADE_PCT=0.90         # 90% of wallet per trade
LEVERAGE=5             # Fixed 5x leverage
MAX_TRADES=4           # Maximum trades per day
TARGET_PCT=0.002       # 0.20% target per trade
STOP_PCT=0.0015        # 0.15% stop loss
MAX_DD_PCT=0.01        # 1% maximum daily drawdown

# Strategy Parameters
VWAP_PERIOD=60         # VWAP period in minutes
RSI_PERIOD=14          # RSI calculation period
SCHED_INTERVAL_SEC=60  # Scheduler check interval

# Database
DATABASE_PATH=./data/solbot.db
KEYSTORE_PATH=./data/keystore.bin

# Pyth Network
PYTH_PROGRAM_ID=FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH
SOL_PRICE_FEED=H6ARHf6YXhGYeQfUzQNGk6rDNnLBQKrenN712K4AQJEG

# Logging
LOG_LEVEL=INFO
LOG_FILE=./data/solbot.log

# Security
ENCRYPTION_PASSWORD_MIN_LENGTH=12
